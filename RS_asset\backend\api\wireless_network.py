from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, asc
from typing import List, Optional
from datetime import datetime, timedelta

from database import get_db
from models.wireless_network import WirelessDevice, WirelessDeviceData, WirelessClient
from schemas.wireless_network import (
    WirelessDeviceCreate, WirelessDeviceUpdate, WirelessDeviceResponse,
    WirelessDeviceDataCreate, WirelessDeviceDataResponse,
    WirelessClientCreate, WirelessClientResponse,
    WirelessDeviceQueryParams, WirelessDataHistoryParams
)
from services.snmp_collection_service import get_snmp_collection_service
from .utils import generic_update_item_async # 新增导入

router = APIRouter(prefix="/api/wireless", tags=["wireless"])

# 无线设备管理
@router.post("/devices", response_model=WirelessDeviceResponse)
async def create_wireless_device(device: WirelessDeviceCreate, db: AsyncSession = Depends(get_db)):
    """创建无线网络设备"""
    db_device = WirelessDevice(**device.model_dump())
    db.add(db_device)
    await db.commit()
    await db.refresh(db_device)
    return db_device


@router.get("/devices", response_model=List[WirelessDeviceResponse])
async def get_wireless_devices(
    floor: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: AsyncSession = Depends(get_db)
):
    """获取无线网络设备列表"""
    query = select(WirelessDevice)
    
    if floor:
        query = query.filter(WirelessDevice.floor == floor)
    if status:
        query = query.filter(WirelessDevice.status == status)
    
    query = query.offset(offset).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


@router.get("/devices/{device_id}", response_model=WirelessDeviceResponse)
async def get_wireless_device(device_id: str, db: AsyncSession = Depends(get_db)):
    """获取单个无线网络设备"""
    result = await db.execute(select(WirelessDevice).filter(WirelessDevice.device_id == device_id))
    device = result.scalars().first()
    
    if not device:
        raise HTTPException(status_code=404, detail=f"无线网络设备 {device_id} 不存在")
    
    return device


@router.put("/devices/{device_id}", response_model=WirelessDeviceResponse)
async def update_wireless_device(
    device_id: str,
    device_update: WirelessDeviceUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新无线网络设备"""
    # 预更新检查可以根据需要添加，例如检查 device_update 中的某些字段的有效性
    # async def _pre_update_wireless_device_checks(db_session: AsyncSession, db_item: WirelessDevice, update_schema: WirelessDeviceUpdate):
    #     pass

    return await generic_update_item_async(
        db=db,
        model_cls=WirelessDevice,
        item_id=device_id,
        update_data_schema=device_update,
        item_name="无线网络设备",
        id_attribute="device_id"  # 指定模型中用于查找的ID字段名
        # pre_update_checks=_pre_update_wireless_device_checks # 如果有预检查，取消注释此行
    )


@router.delete("/devices/{device_id}")
async def delete_wireless_device(device_id: str, db: AsyncSession = Depends(get_db)):
    """删除无线网络设备"""
    result = await db.execute(select(WirelessDevice).filter(WirelessDevice.device_id == device_id))
    device = result.scalars().first()
    
    if not device:
        raise HTTPException(status_code=404, detail=f"无线网络设备 {device_id} 不存在")
    
    await db.delete(device)
    await db.commit()
    return {"message": f"无线网络设备 {device_id} 已删除"}


# 无线设备数据
@router.post("/data", response_model=WirelessDeviceDataResponse)
async def create_wireless_device_data(data: WirelessDeviceDataCreate, db: AsyncSession = Depends(get_db)):
    """创建无线网络设备数据"""
    db_data = WirelessDeviceData(**data.model_dump())
    db.add(db_data)
    await db.commit()
    await db.refresh(db_data)
    return db_data


@router.get("/data", response_model=List[WirelessDeviceDataResponse])
async def get_wireless_device_data(
    device_id: Optional[str] = None,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取无线网络设备数据"""
    query = select(WirelessDeviceData).order_by(desc(WirelessDeviceData.timestamp))
    
    if device_id:
        query = query.filter(WirelessDeviceData.device_id == device_id)
    if start_time:
        query = query.filter(WirelessDeviceData.timestamp >= start_time)
    if end_time:
        query = query.filter(WirelessDeviceData.timestamp <= end_time)
    
    query = query.limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


# 无线客户端
@router.get("/clients", response_model=List[WirelessClientResponse])
async def get_wireless_clients(
    device_id: Optional[str] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取无线客户端列表"""
    query = select(WirelessClient).order_by(desc(WirelessClient.last_seen))
    
    if device_id:
        query = query.filter(WirelessClient.device_id == device_id)
    
    query = query.limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


# 手动触发数据采集
@router.post("/collect/{device_id}")
async def trigger_wireless_data_collection(device_id: str):
    """手动触发无线网络设备数据采集"""
    try:
        # 获取SNMP采集服务实例
        service = get_snmp_collection_service()
        
        # 触发数据采集
        await service.collect_device_data(device_id, "wireless")
        
        return {"message": f"无线网络设备 {device_id} 数据采集任务已触发"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发数据采集失败: {str(e)}")


# 获取历史数据统计
@router.post("/history", response_model=dict)
async def get_wireless_history_data(params: WirelessDataHistoryParams, db: AsyncSession = Depends(get_db)):
    """获取无线网络设备历史数据统计"""
    try:
        # 根据间隔选择时间分组函数
        if params.interval == "hour":
            time_format = "%Y-%m-%d %H:00:00"
            interval_seconds = 3600
        elif params.interval == "day":
            time_format = "%Y-%m-%d 00:00:00"
            interval_seconds = 86400
        elif params.interval == "week":
            time_format = "%Y-%U"  # 按周分组
            interval_seconds = 604800
        elif params.interval == "month":
            time_format = "%Y-%m"
            interval_seconds = 2592000
        else:
            time_format = "%Y-%m-%d %H:00:00"
            interval_seconds = 3600
        
        # 查询数据
        query = select(
            func.date_trunc(params.interval, WirelessDeviceData.timestamp).label('time_interval'),
            func.avg(WirelessDeviceData.clients_count).label('avg_clients'),
            func.avg(WirelessDeviceData.cpu_usage).label('avg_cpu'),
            func.avg(WirelessDeviceData.memory_usage).label('avg_memory'),
            func.max(WirelessDeviceData.rx_bytes).label('max_rx_bytes'),
            func.max(WirelessDeviceData.tx_bytes).label('max_tx_bytes')
        ).filter(
            WirelessDeviceData.device_id == params.device_id,
            WirelessDeviceData.timestamp >= params.start_time,
            WirelessDeviceData.timestamp <= params.end_time
        ).group_by('time_interval').order_by(asc('time_interval'))
        
        result = await db.execute(query)
        data_points = result.fetchall()
        
        # 格式化结果
        formatted_data = {
            "time_intervals": [],
            "clients": [],
            "cpu_usage": [],
            "memory_usage": [],
            "rx_bytes": [],
            "tx_bytes": []
        }
        
        for point in data_points:
            formatted_data["time_intervals"].append(point.time_interval.isoformat())
            formatted_data["clients"].append(round(point.avg_clients, 2) if point.avg_clients else 0)
            formatted_data["cpu_usage"].append(round(point.avg_cpu, 2) if point.avg_cpu else 0)
            formatted_data["memory_usage"].append(round(point.avg_memory, 2) if point.avg_memory else 0)
            formatted_data["rx_bytes"].append(point.max_rx_bytes if point.max_rx_bytes else 0)
            formatted_data["tx_bytes"].append(point.max_tx_bytes if point.max_tx_bytes else 0)
        
        return formatted_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")
