/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* UI设计原型规范 */
    --radius: 8px;

    /* 主色调 */
    --primary: 225 73% 57%; /* #4169E1 皇家蓝 - 主要按钮、导航栏、重要信息高亮 */
    --primary-foreground: 0 0% 100%;

    /* 辅助色 */
    --success: 151 48% 49%; /* #41B883 绿色 - 成功状态、正常运行指示 */
    --success-foreground: 0 0% 100%;

    /* 警示色 */
    --destructive: 355 75% 59%; /* #E34D59 红色 - 错误提示、异常状态、报警信息 */
    --destructive-foreground: 0 0% 100%;

    /* 提醒色 */
    --warning: 42 93% 54%; /* #F7BA1E 黄色 - 警告、待处理状态 */
    --warning-foreground: 0 0% 10%;

    /* 中性色 */
    --background: 220 20% 98%; /* #F5F7FA 浅灰色 - 页面背景 */
    --foreground: 0 0% 10%; /* #1A1A1A 深灰色 - 主要文字 */
    --border: 220 13% 91%; /* #DCDFE6 淡灰色 - 分割线、边框 */

    /* 组件颜色 */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;
    --input: 220 13% 91%;
    --ring: 225 73% 57%;

    /* 表格颜色 */
    --muted: 210 40% 98%; /* #F2F6FC 表头背景色 */
    --muted-foreground: 215 16% 47%;

    /* 状态标签颜色 */
    --status-normal-bg: 151 48% 90%; /* #E1F3D8 */
    --status-normal-text: 151 48% 49%; /* #41B883 */
    --status-error-bg: 355 75% 94%; /* #FCE2E2 */
    --status-error-text: 355 75% 59%; /* #E34D59 */
    --status-warning-bg: 42 93% 94%; /* #FDF6EC */
    --status-warning-text: 42 93% 54%; /* #F7BA1E */
    --status-processing-bg: 225 73% 94%; /* #E8F4FF */
    --status-processing-text: 225 73% 57%; /* #4169E1 */
  }
    --processing-bg: 225 73% 97%; /* #E8F4FF */
    --processing-text: 225 73% 57%; /* #4169E1 */

    /* 丰富多彩的自定义颜色 */
    --vibrant-blue: #4361ee;
    --vibrant-blue-rgb: 67, 97, 238;
    --vibrant-indigo: #3a0ca3;
    --vibrant-indigo-rgb: 58, 12, 163;
    --vibrant-purple: #7209b7;
    --vibrant-purple-rgb: 114, 9, 183;
    --vibrant-pink: #f72585;
    --vibrant-pink-rgb: 247, 37, 133;
    --vibrant-red: #e63946;
    --vibrant-red-rgb: 230, 57, 70;
    --vibrant-orange: #fb8500;
    --vibrant-orange-rgb: 251, 133, 0;
    --vibrant-yellow: #ffbe0b;
    --vibrant-yellow-rgb: 255, 190, 11;
    --vibrant-green: #06d6a0;
    --vibrant-green-rgb: 6, 214, 160;
    --vibrant-teal: #0cb0a9;
    --vibrant-teal-rgb: 12, 176, 169;

    /* 保留原有项目变量 */
    --komodo-green: #33bb73;
    --komodo-darkGray: #1A202C;
    --komodo-lightGray: #F7FAFC;
  }

  .dark {
    /* 现代简约风格暗色模式变量 */
    --background: 222 47% 11%; /* 深色背景 */
    --foreground: 210 40% 98%; /* 浅色文字 */

    --card: 222 47% 11%; /* 深色卡片背景 */
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    /* 主色调 - 皇家蓝 (稍微调亮) */
    --primary: 225 73% 65%;
    --primary-foreground: 222 47% 11%;

    /* 次要色调 - 绿色 (稍微调亮) */
    --secondary: 160 64% 60%;
    --secondary-foreground: 222 47% 11%;

    /* 强调色 - 深色模式下的淡色 */
    --accent: 217 33% 17%;
    --accent-foreground: 225 73% 65%;

    /* 静音色调 */
    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    /* 破坏性操作色调 - 红色 (稍微调亮) */
    --destructive: 354 70% 60%;
    --destructive-foreground: 210 40% 98%;

    /* 边框和输入框 */
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 225 73% 65%;

    /* 丰富多彩的自定义颜色 - 暗色模式 */
    --vibrant-blue: #4cc9f0;
    --vibrant-blue-rgb: 76, 201, 240;
    --vibrant-indigo: #7b2cbf;
    --vibrant-indigo-rgb: 123, 44, 191;
    --vibrant-purple: #c77dff;
    --vibrant-purple-rgb: 199, 125, 255;
    --vibrant-pink: #ff5d8f;
    --vibrant-pink-rgb: 255, 93, 143;
    --vibrant-red: #ff5a5f;
    --vibrant-red-rgb: 255, 90, 95;
    --vibrant-orange: #ff9e00;
    --vibrant-orange-rgb: 255, 158, 0;
    --vibrant-yellow: #ffca3a;
    --vibrant-yellow-rgb: 255, 202, 58;
    --vibrant-green: #38b000;
    --vibrant-green-rgb: 56, 176, 0;
    --vibrant-teal: #00b4d8;
    --vibrant-teal-rgb: 0, 180, 216;
  }

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* 自定义动画 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px 0 rgba(var(--vibrant-blue-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(var(--vibrant-blue-rgb), 0.6);
  }
  100% {
    box-shadow: 0 0 5px 0 rgba(var(--vibrant-blue-rgb), 0.3);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes slide-in-left {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out forwards;
}

@keyframes slide-in-right {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out forwards;
}

@keyframes fade-in-up {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out forwards;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, var(--vibrant-blue) 0%, var(--vibrant-purple) 50%, var(--vibrant-pink) 100%);
}

.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(90deg, var(--vibrant-blue), var(--vibrant-pink));
}

/* 额外的渐变文本样式 */
.gradient-text-blue-purple {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(90deg, var(--vibrant-blue), var(--vibrant-purple));
}

.gradient-text-purple-pink {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(90deg, var(--vibrant-purple), var(--vibrant-pink));
}

.gradient-text-green-teal {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(90deg, var(--vibrant-green), var(--vibrant-teal));
}

.gradient-text-orange-red {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(90deg, var(--vibrant-orange), var(--vibrant-red));
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.dark .glass {
  background: rgba(17, 25, 40, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

