"""add asset category model

Revision ID: 65c370f8b1c2
Revises: 
Create Date: 2025-05-13 15:28:42.532138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '65c370f8b1c2'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('asset_categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('code', sa.String(length=10), nullable=True),
    sa.Column('level', sa.Integer(), nullable=True),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('attributes', sa.JSON(), nullable=True),
    sa.Column('is_system', sa.<PERSON>(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['asset_categories.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_index(op.f('ix_asset_categories_id'), 'asset_categories', ['id'], unique=False)
    op.create_table('cabinets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cabinets_id'), 'cabinets', ['id'], unique=False)
    op.create_index(op.f('ix_cabinets_name'), 'cabinets', ['name'], unique=True)
    op.create_table('racks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('location', sa.String(), nullable=True),
    sa.Column('total_u', sa.Integer(), nullable=True),
    sa.Column('used_u', sa.Integer(), nullable=True),
    sa.Column('utilization', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_racks_id'), 'racks', ['id'], unique=False)
    op.create_index(op.f('ix_racks_name'), 'racks', ['name'], unique=True)
    op.create_table('phone_extensions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('cabinet_id', sa.Integer(), nullable=True),
    sa.Column('position', sa.Integer(), nullable=True),
    sa.Column('extension_number', sa.String(), nullable=True),
    sa.Column('socket_number', sa.String(), nullable=True),
    sa.Column('user', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['cabinet_id'], ['cabinets.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_phone_extensions_extension_number'), 'phone_extensions', ['extension_number'], unique=True)
    op.create_index(op.f('ix_phone_extensions_id'), 'phone_extensions', ['id'], unique=False)
    op.create_table('rack_devices',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.Column('custom_attributes', sa.JSON(), nullable=True),
    sa.Column('rack_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('model', sa.String(), nullable=True),
    sa.Column('manufacturer', sa.String(), nullable=True),
    sa.Column('serial_number', sa.String(), nullable=True),
    sa.Column('position_start', sa.Integer(), nullable=True),
    sa.Column('position_end', sa.Integer(), nullable=True),
    sa.Column('power_consumption', sa.Float(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('user', sa.String(), nullable=True),
    sa.Column('department', sa.String(), nullable=True),
    sa.Column('purchase_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('warranty_expire', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['asset_categories.id'], ),
    sa.ForeignKeyConstraint(['rack_id'], ['racks.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('serial_number')
    )
    op.create_index(op.f('ix_rack_devices_id'), 'rack_devices', ['id'], unique=False)
    op.create_index(op.f('ix_rack_devices_name'), 'rack_devices', ['name'], unique=False)
    op.create_table('maintenance_records',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('device_id', sa.Integer(), nullable=True),
    sa.Column('maintenance_type', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('maintainer', sa.String(), nullable=True),
    sa.Column('maintenance_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('next_maintenance_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['rack_devices.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_maintenance_records_id'), 'maintenance_records', ['id'], unique=False)
    op.drop_table('ups_data')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ups_data',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('device_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('metric_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('value', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('unit', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('timestamp', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='ups_data_pkey')
    )
    op.drop_index(op.f('ix_maintenance_records_id'), table_name='maintenance_records')
    op.drop_table('maintenance_records')
    op.drop_index(op.f('ix_rack_devices_name'), table_name='rack_devices')
    op.drop_index(op.f('ix_rack_devices_id'), table_name='rack_devices')
    op.drop_table('rack_devices')
    op.drop_index(op.f('ix_phone_extensions_id'), table_name='phone_extensions')
    op.drop_index(op.f('ix_phone_extensions_extension_number'), table_name='phone_extensions')
    op.drop_table('phone_extensions')
    op.drop_index(op.f('ix_racks_name'), table_name='racks')
    op.drop_index(op.f('ix_racks_id'), table_name='racks')
    op.drop_table('racks')
    op.drop_index(op.f('ix_cabinets_name'), table_name='cabinets')
    op.drop_index(op.f('ix_cabinets_id'), table_name='cabinets')
    op.drop_table('cabinets')
    op.drop_index(op.f('ix_asset_categories_id'), table_name='asset_categories')
    op.drop_table('asset_categories')
    # ### end Alembic commands ###
