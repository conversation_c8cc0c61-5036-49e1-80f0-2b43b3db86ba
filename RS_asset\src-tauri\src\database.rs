// 数据库模块
// 处理SQLite数据库连接、初始化和迁移

use crate::error::{AppError, AppResult};
use sqlx::sqlite::{SqliteConnectOptions, SqlitePool, SqlitePoolOptions};
use sqlx::{migrate::MigrateDatabase, Sqlite};
use std::path::Path;
use std::str::FromStr;
use tauri::AppHandle;

/// 数据库管理器
#[derive(Debug, Clone)]
pub struct DatabaseManager {
    pool: SqlitePool,
    db_path: String,
}

impl DatabaseManager {
    /// 创建新的数据库管理器实例
    pub async fn new(app_handle: &AppHandle) -> AppResult<Self> {
        let app_dir = app_handle
            .path_resolver()
            .app_data_dir()
            .ok_or_else(|| AppError::Config {
                message: "无法获取应用数据目录".to_string(),
            })?
            .join("database");

        // 确保数据库目录存在
        if !app_dir.exists() {
            std::fs::create_dir_all(&app_dir).map_err(|e| AppError::FileSystem {
                message: format!("创建数据库目录失败: {}", e),
            })?
        }

        let db_path = app_dir.join("rs_asset.db");
        let db_url = format!("sqlite:{}", db_path.display());

        // 如果数据库不存在，创建它
        if !Sqlite::database_exists(&db_url).await.unwrap_or(false) {
            log::info!("创建新数据库: {}", db_url);
            Sqlite::create_database(&db_url).await.map_err(|e| AppError::Database {
                message: format!("创建数据库失败: {}", e),
            })?
        }

        // 配置连接选项
        let connect_options = SqliteConnectOptions::from_str(&db_url)
            .map_err(|e| AppError::Database {
                message: format!("数据库连接选项配置失败: {}", e),
            })?
            .create_if_missing(true)
            .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
            .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
            .busy_timeout(std::time::Duration::from_secs(30))
            .pragma("cache_size", "-64000") // 64MB cache
            .pragma("temp_store", "memory")
            .pragma("mmap_size", "268435456"); // 256MB mmap

        // 创建连接池
        let pool = SqlitePoolOptions::new()
            .max_connections(10)
            .min_connections(1)
            .acquire_timeout(std::time::Duration::from_secs(30))
            .idle_timeout(Some(std::time::Duration::from_secs(600)))
            .max_lifetime(Some(std::time::Duration::from_secs(1800)))
            .connect_with(connect_options)
            .await
            .map_err(|e| AppError::Database {
                message: format!("创建数据库连接池失败: {}", e),
            })?;

        let manager = Self {
            pool,
            db_path: db_path.to_string_lossy().to_string(),
        };

        // 运行数据库迁移
        manager.migrate().await?;

        log::info!("数据库初始化完成: {}", manager.db_path);
        Ok(manager)
    }

    /// 获取数据库连接池
    pub fn pool(&self) -> &SqlitePool {
        &self.pool
    }

    /// 获取数据库路径
    pub fn db_path(&self) -> &str {
        &self.db_path
    }

    /// 运行数据库迁移
    pub async fn migrate(&self) -> AppResult<()> {
        log::info!("开始数据库迁移...");
        
        // 创建基础表结构
        self.create_tables().await?;
        
        // 插入初始数据
        self.insert_initial_data().await?;
        
        log::info!("数据库迁移完成");
        Ok(())
    }

    /// 创建数据库表
    async fn create_tables(&self) -> AppResult<()> {
        let queries = vec![
            // 数据库版本表
            r#"
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version TEXT PRIMARY KEY,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#,

            // 资产分类表
            r#"
            CREATE TABLE IF NOT EXISTS asset_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                parent_id INTEGER,
                icon TEXT,
                color TEXT DEFAULT '#4169E1',
                sort_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES asset_categories(id) ON DELETE SET NULL
            )
            "#,

            // 资产表
            r#"
            CREATE TABLE IF NOT EXISTS assets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                asset_code TEXT UNIQUE NOT NULL,
                qr_code TEXT UNIQUE,
                category_id INTEGER,
                model TEXT,
                brand TEXT,
                serial_number TEXT,
                purchase_date DATE,
                purchase_price DECIMAL(15,2),
                current_value DECIMAL(15,2),
                depreciation_method TEXT DEFAULT 'straight_line',
                useful_life INTEGER DEFAULT 60,
                residual_value DECIMAL(15,2) DEFAULT 0,
                lifecycle_status TEXT DEFAULT 'planning',
                asset_status TEXT DEFAULT 'available',
                location TEXT,
                department TEXT,
                responsible_person TEXT,
                responsible_person_phone TEXT,
                ip_address TEXT,
                mac_address TEXT,
                hostname TEXT,
                operating_system TEXT,
                cpu_info TEXT,
                memory_gb INTEGER,
                storage_type TEXT,
                storage_capacity_gb INTEGER,
                network_ports INTEGER,
                power_consumption INTEGER,
                warranty_start_date DATE,
                warranty_end_date DATE,
                warranty_provider TEXT,
                installation_date DATE,
                maintenance_schedule TEXT,
                last_maintenance_date DATE,
                next_maintenance_date DATE,
                maintenance_interval_days INTEGER DEFAULT 365,
                notes TEXT,
                tags TEXT,
                custom_fields TEXT,
                image_urls TEXT,
                documents TEXT,
                is_critical BOOLEAN DEFAULT FALSE,
                created_by INTEGER,
                updated_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES asset_categories(id) ON DELETE SET NULL,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,
            
            // 监控设备表
            r#"
            CREATE TABLE IF NOT EXISTS monitoring_devices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                asset_id INTEGER,
                device_type TEXT NOT NULL,
                ip_address TEXT NOT NULL,
                port INTEGER DEFAULT 161,
                community TEXT DEFAULT 'public',
                snmp_version TEXT DEFAULT 'v2c',
                username TEXT,
                auth_protocol TEXT,
                auth_password TEXT,
                priv_protocol TEXT,
                priv_password TEXT,
                polling_interval INTEGER DEFAULT 300,
                timeout INTEGER DEFAULT 10,
                retries INTEGER DEFAULT 3,
                enabled BOOLEAN DEFAULT TRUE,
                last_poll_time DATETIME,
                status TEXT DEFAULT 'unknown',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (asset_id) REFERENCES assets(id)
            )
            "#,
            
            // SNMP数据表
            r#"
            CREATE TABLE IF NOT EXISTS snmp_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id INTEGER NOT NULL,
                oid TEXT NOT NULL,
                value TEXT,
                value_type TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (device_id) REFERENCES monitoring_devices(id)
            )
            "#,
            
            // 网络数据表
            r#"
            CREATE TABLE IF NOT EXISTS network_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id INTEGER NOT NULL,
                interface_name TEXT,
                interface_index INTEGER,
                bytes_in BIGINT DEFAULT 0,
                bytes_out BIGINT DEFAULT 0,
                packets_in BIGINT DEFAULT 0,
                packets_out BIGINT DEFAULT 0,
                errors_in BIGINT DEFAULT 0,
                errors_out BIGINT DEFAULT 0,
                discards_in BIGINT DEFAULT 0,
                discards_out BIGINT DEFAULT 0,
                speed BIGINT,
                mtu INTEGER,
                admin_status TEXT,
                oper_status TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (device_id) REFERENCES monitoring_devices(id)
            )
            "#,
            
            // 系统信息表
            r#"
            CREATE TABLE IF NOT EXISTS system_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                device_id INTEGER NOT NULL,
                system_name TEXT,
                system_description TEXT,
                system_uptime BIGINT,
                system_contact TEXT,
                system_location TEXT,
                cpu_usage REAL,
                memory_total BIGINT,
                memory_used BIGINT,
                disk_total BIGINT,
                disk_used BIGINT,
                temperature REAL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (device_id) REFERENCES monitoring_devices(id)
            )
            "            "#,

            // 维修工单表
            r#"
            CREATE TABLE IF NOT EXISTS maintenance_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_number TEXT UNIQUE NOT NULL,
                asset_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                priority TEXT DEFAULT 'medium',
                status TEXT DEFAULT 'pending',
                order_type TEXT DEFAULT 'corrective',
                fault_type TEXT,
                fault_description TEXT,
                reported_by INTEGER,
                assigned_to INTEGER,
                assigned_team TEXT,
                estimated_hours REAL,
                actual_hours REAL,
                estimated_cost DECIMAL(15,2),
                actual_cost DECIMAL(15,2),
                scheduled_start_time DATETIME,
                scheduled_end_time DATETIME,
                actual_start_time DATETIME,
                actual_end_time DATETIME,
                completion_notes TEXT,
                customer_satisfaction INTEGER,
                customer_feedback TEXT,
                attachments TEXT,
                created_by INTEGER,
                updated_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
                FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 维修工单状态变更记录表
            r#"
            CREATE TABLE IF NOT EXISTS maintenance_order_status_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                from_status TEXT,
                to_status TEXT NOT NULL,
                reason TEXT,
                notes TEXT,
                changed_by INTEGER,
                changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES maintenance_orders(id) ON DELETE CASCADE,
                FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 备品备件表
            r#"
            CREATE TABLE IF NOT EXISTS spare_parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                part_number TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category TEXT,
                brand TEXT,
                model TEXT,
                specification TEXT,
                unit TEXT DEFAULT 'pcs',
                unit_price DECIMAL(15,2),
                min_stock_level INTEGER DEFAULT 0,
                max_stock_level INTEGER,
                current_stock INTEGER DEFAULT 0,
                location TEXT,
                supplier TEXT,
                supplier_contact TEXT,
                lead_time_days INTEGER DEFAULT 7,
                shelf_life_months INTEGER,
                is_critical BOOLEAN DEFAULT FALSE,
                notes TEXT,
                image_urls TEXT,
                created_by INTEGER,
                updated_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 备件出入库记录表
            r#"
            CREATE TABLE IF NOT EXISTS spare_part_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                part_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price DECIMAL(15,2),
                total_amount DECIMAL(15,2),
                reference_type TEXT,
                reference_id INTEGER,
                supplier TEXT,
                batch_number TEXT,
                expiry_date DATE,
                notes TEXT,
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (part_id) REFERENCES spare_parts(id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 巡检计划表
            r#"
            CREATE TABLE IF NOT EXISTS inspection_plans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                asset_ids TEXT,
                inspection_type TEXT DEFAULT 'routine',
                frequency_type TEXT DEFAULT 'monthly',
                frequency_value INTEGER DEFAULT 1,
                checklist_template TEXT,
                assigned_to INTEGER,
                assigned_team TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                next_inspection_date DATE,
                created_by INTEGER,
                updated_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 巡检记录表
            r#"
            CREATE TABLE IF NOT EXISTS inspection_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plan_id INTEGER,
                asset_id INTEGER NOT NULL,
                inspector_id INTEGER,
                inspection_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'completed',
                overall_result TEXT DEFAULT 'normal',
                checklist_data TEXT,
                issues_found TEXT,
                recommendations TEXT,
                photos TEXT,
                signature TEXT,
                location_verified BOOLEAN DEFAULT FALSE,
                duration_minutes INTEGER,
                weather_conditions TEXT,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (plan_id) REFERENCES inspection_plans(id) ON DELETE SET NULL,
                FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
                FOREIGN KEY (inspector_id) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 用户表
            r#"
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT,
                employee_id TEXT UNIQUE,
                position TEXT,
                role TEXT DEFAULT 'user',
                department TEXT,
                phone TEXT,
                avatar_url TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                last_login_at DATETIME,
                last_login_ip TEXT,
                failed_login_attempts INTEGER DEFAULT 0,
                locked_until DATETIME,
                password_changed_at DATETIME,
                must_change_password BOOLEAN DEFAULT FALSE,
                two_factor_enabled BOOLEAN DEFAULT FALSE,
                two_factor_secret TEXT,
                preferences TEXT,
                created_by INTEGER,
                updated_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,
            
            // 操作日志表
            r#"
            CREATE TABLE IF NOT EXISTS audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                resource_type TEXT,
                resource_id INTEGER,
                details TEXT,
                ip_address TEXT,
                user_agent TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
            "            "#,

            // 角色表
            r#"
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                display_name TEXT NOT NULL,
                description TEXT,
                permissions TEXT,
                is_system BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INTEGER,
                updated_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 用户角色关联表
            r#"
            CREATE TABLE IF NOT EXISTS user_roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                role_id INTEGER NOT NULL,
                assigned_by INTEGER,
                assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME,
                UNIQUE(user_id, role_id),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 通知表
            r#"
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                content TEXT,
                type TEXT DEFAULT 'info',
                priority TEXT DEFAULT 'normal',
                recipient_id INTEGER,
                recipient_type TEXT DEFAULT 'user',
                sender_id INTEGER,
                related_type TEXT,
                related_id INTEGER,
                is_read BOOLEAN DEFAULT FALSE,
                read_at DATETIME,
                action_url TEXT,
                action_text TEXT,
                expires_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 文档管理表
            r#"
            CREATE TABLE IF NOT EXISTS documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                mime_type TEXT,
                category TEXT,
                tags TEXT,
                version TEXT DEFAULT '1.0',
                is_public BOOLEAN DEFAULT FALSE,
                related_type TEXT,
                related_id INTEGER,
                uploaded_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 系统设置表
            r#"
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                module TEXT NOT NULL,
                setting_key TEXT NOT NULL,
                setting_value TEXT,
                data_type TEXT DEFAULT 'string',
                description TEXT,
                is_public BOOLEAN DEFAULT FALSE,
                validation_rule TEXT,
                default_value TEXT,
                updated_by INTEGER,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(module, setting_key),
                FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
            )
            "#,

            // 配置表
            r#""
            CREATE TABLE IF NOT EXISTS configurations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                category TEXT DEFAULT 'general',
                is_encrypted BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        ];

        for query in queries {
            sqlx::query(query)
                .execute(&self.pool)
                .await
                .map_err(|e| AppError::Database {
                    message: format!("创建表失败: {}", e),
                })?;
        }

        // 创建索引
        self.create_indexes().await?;

        Ok(())
    }

    /// 创建数据库索引
    async fn create_indexes(&self) -> AppResult<()> {
        let indexes = vec![
            // 资产相关索引
            "CREATE INDEX IF NOT EXISTS idx_assets_code ON assets(asset_code)",
            "CREATE INDEX IF NOT EXISTS idx_assets_qr_code ON assets(qr_code)",
            "CREATE INDEX IF NOT EXISTS idx_assets_category ON assets(category_id)",
            "CREATE INDEX IF NOT EXISTS idx_assets_status ON assets(asset_status)",
            "CREATE INDEX IF NOT EXISTS idx_assets_location ON assets(location)",
            "CREATE INDEX IF NOT EXISTS idx_assets_department ON assets(department)",
            "CREATE INDEX IF NOT EXISTS idx_assets_responsible ON assets(responsible_person)",
            "CREATE INDEX IF NOT EXISTS idx_assets_created_at ON assets(created_at)",

            // 资产分类索引
            "CREATE INDEX IF NOT EXISTS idx_asset_categories_parent ON asset_categories(parent_id)",
            "CREATE INDEX IF NOT EXISTS idx_asset_categories_active ON asset_categories(is_active)",

            // 监控设备索引
            "CREATE INDEX IF NOT EXISTS idx_monitoring_devices_ip ON monitoring_devices(ip_address)",
            "CREATE INDEX IF NOT EXISTS idx_monitoring_devices_asset ON monitoring_devices(asset_id)",
            "CREATE INDEX IF NOT EXISTS idx_snmp_data_device ON snmp_data(device_id)",
            "CREATE INDEX IF NOT EXISTS idx_snmp_data_timestamp ON snmp_data(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_network_data_device ON network_data(device_id)",
            "CREATE INDEX IF NOT EXISTS idx_network_data_timestamp ON network_data(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_system_info_device ON system_info(device_id)",
            "CREATE INDEX IF NOT EXISTS idx_system_info_timestamp ON system_info(timestamp)",

            // 维修工单索引
            "CREATE INDEX IF NOT EXISTS idx_maintenance_orders_number ON maintenance_orders(order_number)",
            "CREATE INDEX IF NOT EXISTS idx_maintenance_orders_asset ON maintenance_orders(asset_id)",
            "CREATE INDEX IF NOT EXISTS idx_maintenance_orders_status ON maintenance_orders(status)",
            "CREATE INDEX IF NOT EXISTS idx_maintenance_orders_assigned ON maintenance_orders(assigned_to)",
            "CREATE INDEX IF NOT EXISTS idx_maintenance_orders_created_at ON maintenance_orders(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_maintenance_order_status_logs_order ON maintenance_order_status_logs(order_id)",

            // 备件索引
            "CREATE INDEX IF NOT EXISTS idx_spare_parts_number ON spare_parts(part_number)",
            "CREATE INDEX IF NOT EXISTS idx_spare_parts_category ON spare_parts(category)",
            "CREATE INDEX IF NOT EXISTS idx_spare_parts_stock ON spare_parts(current_stock)",
            "CREATE INDEX IF NOT EXISTS idx_spare_part_transactions_part ON spare_part_transactions(part_id)",
            "CREATE INDEX IF NOT EXISTS idx_spare_part_transactions_type ON spare_part_transactions(transaction_type)",
            "CREATE INDEX IF NOT EXISTS idx_spare_part_transactions_created_at ON spare_part_transactions(created_at)",

            // 巡检索引
            "CREATE INDEX IF NOT EXISTS idx_inspection_plans_active ON inspection_plans(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_inspection_plans_assigned ON inspection_plans(assigned_to)",
            "CREATE INDEX IF NOT EXISTS idx_inspection_records_plan ON inspection_records(plan_id)",
            "CREATE INDEX IF NOT EXISTS idx_inspection_records_asset ON inspection_records(asset_id)",
            "CREATE INDEX IF NOT EXISTS idx_inspection_records_inspector ON inspection_records(inspector_id)",
            "CREATE INDEX IF NOT EXISTS idx_inspection_records_date ON inspection_records(inspection_date)",

            // 用户相关索引
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_employee_id ON users(employee_id)",
            "CREATE INDEX IF NOT EXISTS idx_users_department ON users(department)",
            "CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_user_roles_user ON user_roles(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_user_roles_role ON user_roles(role_id)",

            // 通知索引
            "CREATE INDEX IF NOT EXISTS idx_notifications_recipient ON notifications(recipient_id)",
            "CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(is_read)",
            "CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at)",

            // 文档索引
            "CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category)",
            "CREATE INDEX IF NOT EXISTS idx_documents_related ON documents(related_type, related_id)",
            "CREATE INDEX IF NOT EXISTS idx_documents_uploaded_by ON documents(uploaded_by)",

            // 系统相关索引
            "CREATE INDEX IF NOT EXISTS idx_audit_logs_user ON audit_logs(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id)",
            "CREATE INDEX IF NOT EXISTS idx_configurations_key ON configurations(key)",
            "CREATE INDEX IF NOT EXISTS idx_configurations_category ON configurations(category)",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_module ON system_settings(module)",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(module, setting_key)",
        ];

        for index in indexes {
            sqlx::query(index)
                .execute(&self.pool)
                .await
                .map_err(|e| AppError::Database {
                    message: format!("创建索引失败: {}", e),
                })?;
        }

        Ok(())
    }

    /// 插入初始数据
    async fn insert_initial_data(&self) -> AppResult<()> {
        // 插入默认资产分类
        let categories = vec![
            ("网络设备", "路由器、交换机、防火墙等网络设备", None),
            ("服务器", "物理服务器和虚拟服务器", None),
            ("终端设备", "台式机、笔记本电脑、平板等", None),
            ("存储设备", "存储阵列、磁带库等存储设备", None),
            ("安防设备", "监控摄像头、门禁系统等", None),
            ("办公设备", "打印机、扫描仪、投影仪等", None),
        ];

        for (name, description, parent_id) in categories {
            sqlx::query(
                "INSERT OR IGNORE INTO asset_categories (name, description, parent_id) VALUES (?, ?, ?)"
            )
            .bind(name)
            .bind(description)
            .bind(parent_id)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入资产分类失败: {}", e),
            })?;
        }

        // 插入默认管理员用户
        let admin_password = bcrypt::hash("admin123", bcrypt::DEFAULT_COST)
            .map_err(|e| AppError::Internal {
                message: format!("密码哈希失败: {}", e),
            })?;

        sqlx::query(
            r#"
            INSERT OR IGNORE INTO users (username, email, password_hash, full_name, role) 
            VALUES ('admin', '<EMAIL>', ?, '系统管理员', 'admin')
            "#
        )
        .bind(admin_password)
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("插入管理员用户失败: {}", e),
        })?;

        // 插入默认角色
        let roles = vec![
            ("admin", "系统管理员", "拥有系统所有权限", r#"["*"]"#, true),
            ("manager", "部门经理", "部门管理权限", r#"["asset:read","asset:write","maintenance:read","maintenance:write","inspection:read","inspection:write","user:read"]"#, false),
            ("technician", "技术员", "设备维护权限", r#"["asset:read","maintenance:read","maintenance:write","inspection:read","inspection:write"]"#, false),
            ("operator", "操作员", "基础操作权限", r#"["asset:read","inspection:read","inspection:write"]"#, false),
            ("viewer", "查看者", "只读权限", r#"["asset:read","maintenance:read","inspection:read"]"#, false),
        ];

        for (name, display_name, description, permissions, is_system) in roles {
            sqlx::query(
                "INSERT OR IGNORE INTO roles (name, display_name, description, permissions, is_system) VALUES (?, ?, ?, ?, ?)"
            )
            .bind(name)
            .bind(display_name)
            .bind(description)
            .bind(permissions)
            .bind(is_system)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入角色失败: {}", e),
            })?;
        }

        // 为管理员用户分配角色
        sqlx::query(
            r#"
            INSERT OR IGNORE INTO user_roles (user_id, role_id)
            SELECT u.id, r.id FROM users u, roles r
            WHERE u.username = 'admin' AND r.name = 'admin'
            "#
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("分配管理员角色失败: {}", e),
        })?;

        // 插入系统设置
        let system_settings = vec![
            ("system", "app_name", "RS 资产管理系统", "string", "应用程序名称", "false"),
            ("system", "app_version", "2.0.0", "string", "应用程序版本", "true"),
            ("system", "company_name", "燃石科技", "string", "公司名称", "false"),
            ("system", "company_logo", "", "string", "公司Logo URL", "false"),
            ("system", "timezone", "Asia/Shanghai", "string", "系统时区", "false"),
            ("system", "language", "zh-CN", "string", "系统语言", "false"),
            ("system", "date_format", "YYYY-MM-DD", "string", "日期格式", "false"),
            ("system", "time_format", "HH:mm:ss", "string", "时间格式", "false"),

            ("security", "password_min_length", "8", "number", "密码最小长度", "false"),
            ("security", "password_require_uppercase", "true", "boolean", "密码需要大写字母", "false"),
            ("security", "password_require_lowercase", "true", "boolean", "密码需要小写字母", "false"),
            ("security", "password_require_numbers", "true", "boolean", "密码需要数字", "false"),
            ("security", "password_require_symbols", "false", "boolean", "密码需要特殊字符", "false"),
            ("security", "session_timeout_minutes", "480", "number", "会话超时时间(分钟)", "false"),
            ("security", "max_login_attempts", "5", "number", "最大登录尝试次数", "false"),
            ("security", "lockout_duration_minutes", "30", "number", "账户锁定时长(分钟)", "false"),

            ("monitoring", "snmp_default_community", "public", "string", "默认SNMP团体名", "false"),
            ("monitoring", "snmp_default_port", "161", "number", "默认SNMP端口", "false"),
            ("monitoring", "snmp_default_timeout", "10", "number", "默认SNMP超时时间(秒)", "false"),
            ("monitoring", "snmp_default_retries", "3", "number", "默认SNMP重试次数", "false"),
            ("monitoring", "polling_interval_seconds", "300", "number", "监控轮询间隔(秒)", "false"),
            ("monitoring", "data_retention_days", "90", "number", "监控数据保留天数", "false"),

            ("maintenance", "default_priority", "medium", "string", "默认工单优先级", "false"),
            ("maintenance", "auto_assign_enabled", "false", "boolean", "是否启用自动分配", "false"),
            ("maintenance", "notification_enabled", "true", "boolean", "是否启用通知", "false"),
            ("maintenance", "sla_response_hours", "4", "number", "SLA响应时间(小时)", "false"),
            ("maintenance", "sla_resolution_hours", "24", "number", "SLA解决时间(小时)", "false"),

            ("backup", "auto_backup_enabled", "true", "boolean", "是否启用自动备份", "false"),
            ("backup", "backup_interval_hours", "24", "number", "备份间隔(小时)", "false"),
            ("backup", "backup_retention_days", "30", "number", "备份保留天数", "false"),
            ("backup", "backup_location", "./backups", "string", "备份存储位置", "false"),
        ];

        for (module, setting_key, setting_value, data_type, description, is_public) in system_settings {
            sqlx::query(
                "INSERT OR IGNORE INTO system_settings (module, setting_key, setting_value, data_type, description, is_public) VALUES (?, ?, ?, ?, ?, ?)"
            )
            .bind(module)
            .bind(setting_key)
            .bind(setting_value)
            .bind(data_type)
            .bind(description)
            .bind(is_public == "true")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入系统设置失败: {}", e),
            })?;
        }

        // 插入默认配置
        let configs = vec![
            ("app_name", "RS 资产管理系统", "应用程序名称", "general"),
            ("app_version", "2.0.0", "应用程序版本", "general"),
            ("snmp_default_community", "public", "默认SNMP团体名", "snmp"),
            ("snmp_default_port", "161", "默认SNMP端口", "snmp"),
            ("snmp_default_timeout", "10", "默认SNMP超时时间(秒)", "snmp"),
            ("snmp_default_retries", "3", "默认SNMP重试次数", "snmp"),
            ("monitoring_interval", "300", "监控轮询间隔(秒)", "monitoring"),
            ("data_retention_days", "90", "数据保留天数", "general"),
            ("backup_enabled", "true", "是否启用自动备份", "backup"),
            ("backup_interval_hours", "24", "备份间隔(小时)", "backup"),
        ];

        for (key, value, description, category) in configs {
            sqlx::query(
                "INSERT OR IGNORE INTO configurations (key, value, description, category) VALUES (?, ?, ?, ?)"
            )
            .bind(key)
            .bind(value)
            .bind(description)
            .bind(category)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入配置失败: {}", e),
            })?;
        }

        Ok(())
    }

    /// 备份数据库
    pub async fn backup(&self, backup_path: &Path) -> AppResult<()> {
        log::info!("开始备份数据库到: {}", backup_path.display());
        
        // 确保备份目录存在
        if let Some(parent) = backup_path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| AppError::FileSystem {
                message: format!("创建备份目录失败: {}", e),
            })?;
        }

        // 复制数据库文件
        std::fs::copy(&self.db_path, backup_path).map_err(|e| AppError::FileSystem {
            message: format!("备份数据库失败: {}", e),
        })?;

        log::info!("数据库备份完成");
        Ok(())
    }

    /// 恢复数据库
    pub async fn restore(&self, backup_path: &Path) -> AppResult<()> {
        log::info!("开始从备份恢复数据库: {}", backup_path.display());
        
        if !backup_path.exists() {
            return Err(AppError::FileSystem {
                message: "备份文件不存在".to_string(),
            });
        }

        // 关闭当前连接池
        self.pool.close().await;

        // 复制备份文件
        std::fs::copy(backup_path, &self.db_path).map_err(|e| AppError::FileSystem {
            message: format!("恢复数据库失败: {}", e),
        })?;

        log::info!("数据库恢复完成");
        Ok(())
    }

    /// 清理旧数据
    pub async fn cleanup_old_data(&self, retention_days: i64) -> AppResult<()> {
        log::info!("开始清理{}天前的旧数据", retention_days);
        
        let cutoff_date = chrono::Utc::now() - chrono::Duration::days(retention_days);
        let cutoff_str = cutoff_date.format("%Y-%m-%d %H:%M:%S").to_string();

        let tables = vec![
            "snmp_data",
            "network_data", 
            "system_info",
            "audit_logs",
        ];

        for table in tables {
            let query = format!("DELETE FROM {} WHERE timestamp < ?", table);
            let result = sqlx::query(&query)
                .bind(&cutoff_str)
                .execute(&self.pool)
                .await
                .map_err(|e| AppError::Database {
                    message: format!("清理表{}失败: {}", table, e),
                })?;
            
            log::info!("从表{}清理了{}条记录", table, result.rows_affected());
        }

        // 执行VACUUM以回收空间
        sqlx::query("VACUUM")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("VACUUM操作失败: {}", e),
            })?;

        log::info!("数据清理完成");
        Ok(())
    }

    /// 获取数据库统计信息
    pub async fn get_stats(&self) -> AppResult<DatabaseStats> {
        let mut stats = DatabaseStats::default();

        // 获取各表的记录数
        let tables = vec![
            "assets",
            "asset_categories", 
            "monitoring_devices",
            "snmp_data",
            "network_data",
            "system_info",
            "users",
            "audit_logs",
            "configurations",
        ];

        for table in tables {
            let query = format!("SELECT COUNT(*) as count FROM {}", table);
            let row: (i64,) = sqlx::query_as(&query)
                .fetch_one(&self.pool)
                .await
                .map_err(|e| AppError::Database {
                    message: format!("获取表{}统计失败: {}", table, e),
                })?;
            
            match table {
                "assets" => stats.asset_count = row.0,
                "asset_categories" => stats.category_count = row.0,
                "monitoring_devices" => stats.device_count = row.0,
                "snmp_data" => stats.snmp_data_count = row.0,
                "network_data" => stats.network_data_count = row.0,
                "system_info" => stats.system_info_count = row.0,
                "users" => stats.user_count = row.0,
                "audit_logs" => stats.audit_log_count = row.0,
                "configurations" => stats.config_count = row.0,
                _ => {}
            }
        }

        // 获取数据库文件大小
        if let Ok(metadata) = std::fs::metadata(&self.db_path) {
            stats.db_size_bytes = metadata.len();
        }

        Ok(stats)
    }
}

/// 数据库统计信息
#[derive(Debug, Clone, Default, serde::Serialize, serde::Deserialize)]
pub struct DatabaseStats {
    pub asset_count: i64,
    pub category_count: i64,
    pub device_count: i64,
    pub snmp_data_count: i64,
    pub network_data_count: i64,
    pub system_info_count: i64,
    pub user_count: i64,
    pub audit_log_count: i64,
    pub config_count: i64,
    pub db_size_bytes: u64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    
    #[tokio::test]
    async fn test_database_creation() {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        let db_url = format!("sqlite:{}", db_path.display());
        
        // 测试数据库创建
        assert!(!Sqlite::database_exists(&db_url).await.unwrap_or(false));
        Sqlite::create_database(&db_url).await.unwrap();
        assert!(Sqlite::database_exists(&db_url).await.unwrap_or(false));
    }
}