// 维修工单管理相关的 Tauri 命令

use crate::error::{AppError, AppResult};
use crate::models::{MaintenanceOrder, PaginationParams, PaginationResponse};
use crate::services::{MaintenanceService, CreateMaintenanceOrderRequest, UpdateMaintenanceOrderRequest, MaintenanceOrderFilter, MaintenanceStats};
use crate::AppState;
use tauri::State;

/// 创建维修工单
#[tauri::command]
pub async fn create_maintenance_order(
    request: CreateMaintenanceOrderRequest,
    state: State<'_, AppState>,
) -> Result<MaintenanceOrder, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    let created_by = 1; // TODO: 从会话中获取当前用户ID
    
    service.create_order(request, created_by)
        .await
        .map_err(|e| e.to_string())
}

/// 根据ID获取维修工单
#[tauri::command]
pub async fn get_maintenance_order(
    id: i64,
    state: State<'_, AppState>,
) -> Result<MaintenanceOrder, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    
    service.get_order_by_id(id)
        .await
        .map_err(|e| e.to_string())
}

/// 更新维修工单
#[tauri::command]
pub async fn update_maintenance_order(
    id: i64,
    request: UpdateMaintenanceOrderRequest,
    state: State<'_, AppState>,
) -> Result<MaintenanceOrder, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    let updated_by = 1; // TODO: 从会话中获取当前用户ID
    
    service.update_order(id, request, updated_by)
        .await
        .map_err(|e| e.to_string())
}

/// 分页查询维修工单
#[tauri::command]
pub async fn list_maintenance_orders(
    filter: Option<MaintenanceOrderFilter>,
    pagination: PaginationParams,
    state: State<'_, AppState>,
) -> Result<PaginationResponse<MaintenanceOrder>, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    
    service.list_orders(filter, pagination)
        .await
        .map_err(|e| e.to_string())
}

/// 获取维修工单统计信息
#[tauri::command]
pub async fn get_maintenance_stats(
    state: State<'_, AppState>,
) -> Result<MaintenanceStats, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    
    service.get_maintenance_stats()
        .await
        .map_err(|e| e.to_string())
}

/// 分配维修工单
#[tauri::command]
pub async fn assign_maintenance_order(
    id: i64,
    assigned_to: i64,
    assigned_team: Option<String>,
    state: State<'_, AppState>,
) -> Result<MaintenanceOrder, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    let updated_by = 1; // TODO: 从会话中获取当前用户ID
    
    let request = UpdateMaintenanceOrderRequest {
        assigned_to: Some(assigned_to),
        assigned_team,
        status: Some("assigned".to_string()),
        ..Default::default()
    };
    
    service.update_order(id, request, updated_by)
        .await
        .map_err(|e| e.to_string())
}

/// 开始维修工单
#[tauri::command]
pub async fn start_maintenance_order(
    id: i64,
    state: State<'_, AppState>,
) -> Result<MaintenanceOrder, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    let updated_by = 1; // TODO: 从会话中获取当前用户ID
    
    let request = UpdateMaintenanceOrderRequest {
        status: Some("in_progress".to_string()),
        actual_start_time: Some(chrono::Utc::now().naive_utc().to_string()),
        ..Default::default()
    };
    
    service.update_order(id, request, updated_by)
        .await
        .map_err(|e| e.to_string())
}

/// 完成维修工单
#[tauri::command]
pub async fn complete_maintenance_order(
    id: i64,
    completion_notes: Option<String>,
    actual_hours: Option<f64>,
    actual_cost: Option<f64>,
    customer_satisfaction: Option<i32>,
    customer_feedback: Option<String>,
    state: State<'_, AppState>,
) -> Result<MaintenanceOrder, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    let updated_by = 1; // TODO: 从会话中获取当前用户ID
    
    let request = UpdateMaintenanceOrderRequest {
        status: Some("completed".to_string()),
        actual_end_time: Some(chrono::Utc::now().naive_utc().to_string()),
        completion_notes,
        actual_hours,
        actual_cost,
        customer_satisfaction,
        customer_feedback,
        ..Default::default()
    };
    
    service.update_order(id, request, updated_by)
        .await
        .map_err(|e| e.to_string())
}

/// 取消维修工单
#[tauri::command]
pub async fn cancel_maintenance_order(
    id: i64,
    reason: String,
    state: State<'_, AppState>,
) -> Result<MaintenanceOrder, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    let updated_by = 1; // TODO: 从会话中获取当前用户ID
    
    let request = UpdateMaintenanceOrderRequest {
        status: Some("cancelled".to_string()),
        completion_notes: Some(format!("取消原因: {}", reason)),
        ..Default::default()
    };
    
    service.update_order(id, request, updated_by)
        .await
        .map_err(|e| e.to_string())
}

/// 获取工单状态变更历史
#[tauri::command]
pub async fn get_maintenance_order_status_history(
    order_id: i64,
    state: State<'_, AppState>,
) -> Result<Vec<MaintenanceOrderStatusLog>, String> {
    let rows = sqlx::query(
        r#"
        SELECT mosl.*, u.full_name as changed_by_name
        FROM maintenance_order_status_logs mosl
        LEFT JOIN users u ON mosl.changed_by = u.id
        WHERE mosl.order_id = ?
        ORDER BY mosl.changed_at DESC
        "#
    )
    .bind(order_id)
    .fetch_all(state.database.pool())
    .await
    .map_err(|e| format!("查询状态变更历史失败: {}", e))?;

    let mut history = Vec::new();
    for row in rows {
        history.push(MaintenanceOrderStatusLog::from_row(&row).map_err(|e| e.to_string())?);
    }

    Ok(history)
}

/// 获取我的维修工单
#[tauri::command]
pub async fn get_my_maintenance_orders(
    pagination: PaginationParams,
    state: State<'_, AppState>,
) -> Result<PaginationResponse<MaintenanceOrder>, String> {
    let user_id = 1; // TODO: 从会话中获取当前用户ID
    let service = MaintenanceService::new(state.database.pool().clone());
    
    let filter = MaintenanceOrderFilter {
        assigned_to: Some(user_id),
        ..Default::default()
    };
    
    service.list_orders(Some(filter), pagination)
        .await
        .map_err(|e| e.to_string())
}

/// 获取逾期的维修工单
#[tauri::command]
pub async fn get_overdue_maintenance_orders(
    pagination: PaginationParams,
    state: State<'_, AppState>,
) -> Result<PaginationResponse<MaintenanceOrder>, String> {
    let service = MaintenanceService::new(state.database.pool().clone());
    
    // 查询逾期工单的逻辑需要在服务层实现
    // 这里简化处理，实际应该在 MaintenanceService 中添加专门的方法
    let filter = MaintenanceOrderFilter {
        status: Some("in_progress".to_string()),
        ..Default::default()
    };
    
    service.list_orders(Some(filter), pagination)
        .await
        .map_err(|e| e.to_string())
}

/// 维修工单状态变更日志
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct MaintenanceOrderStatusLog {
    pub id: i64,
    pub order_id: i64,
    pub from_status: Option<String>,
    pub to_status: String,
    pub reason: Option<String>,
    pub notes: Option<String>,
    pub changed_by: Option<i64>,
    pub changed_by_name: Option<String>,
    pub changed_at: chrono::NaiveDateTime,
}

impl MaintenanceOrderStatusLog {
    pub fn from_row(row: &sqlx::Row) -> AppResult<Self> {
        Ok(Self {
            id: row.try_get("id")?,
            order_id: row.try_get("order_id")?,
            from_status: row.try_get("from_status")?,
            to_status: row.try_get("to_status")?,
            reason: row.try_get("reason")?,
            notes: row.try_get("notes")?,
            changed_by: row.try_get("changed_by")?,
            changed_by_name: row.try_get("changed_by_name")?,
            changed_at: row.try_get("changed_at")?,
        })
    }
}

// 为 UpdateMaintenanceOrderRequest 实现 Default
impl Default for UpdateMaintenanceOrderRequest {
    fn default() -> Self {
        Self {
            title: None,
            description: None,
            priority: None,
            status: None,
            assigned_to: None,
            assigned_team: None,
            estimated_hours: None,
            actual_hours: None,
            estimated_cost: None,
            actual_cost: None,
            scheduled_start_time: None,
            scheduled_end_time: None,
            actual_start_time: None,
            actual_end_time: None,
            completion_notes: None,
            customer_satisfaction: None,
            customer_feedback: None,
        }
    }
}

// 为 MaintenanceOrderFilter 实现 Default
impl Default for MaintenanceOrderFilter {
    fn default() -> Self {
        Self {
            keyword: None,
            asset_id: None,
            status: None,
            priority: None,
            order_type: None,
            assigned_to: None,
            date_from: None,
            date_to: None,
        }
    }
}
