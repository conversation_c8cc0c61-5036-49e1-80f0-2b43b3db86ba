from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete
from typing import List, Optional
import logging
from enum import Enum

from database import get_db
from models.asset import Asset, AssetCategory, MaintenanceRecord
from schemas.asset import (
    AssetCreate, AssetUpdate, AssetResponse,
    AssetCategoryCreate, AssetCategoryUpdate, AssetCategoryResponse,
    MaintenanceRecordCreate, MaintenanceRecordResponse
)

router = APIRouter(prefix="/api", tags=["assets"])
logger = logging.getLogger(__name__)

# 资产分类API端点
@router.post("/asset-categories", response_model=AssetCategoryResponse)
async def create_asset_category(
    category: AssetCategoryCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建新的资产分类"""
    try:
        db_category = AssetCategory(**category.model_dump())
        db.add(db_category)
        await db.commit()
        await db.refresh(db_category)
        return db_category
    except Exception as e:
        logger.error(f"创建资产分类失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建资产分类失败: {str(e)}"
        )

@router.get("/asset-categories", response_model=List[AssetCategoryResponse])
async def get_asset_categories(db: AsyncSession = Depends(get_db)):
    """获取所有资产分类"""
    try:
        result = await db.execute(select(AssetCategory))
        categories = result.scalars().all()
        return categories
    except Exception as e:
        logger.error(f"获取资产分类失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产分类失败: {str(e)}"
        )

@router.get("/asset-categories/{category_id}", response_model=AssetCategoryResponse)
async def get_asset_category(category_id: int, db: AsyncSession = Depends(get_db)):
    """获取特定资产分类"""
    try:
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == category_id))
        category = result.scalars().first()
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{category_id}的资产分类不存在"
            )
        return category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取资产分类失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产分类失败: {str(e)}"
        )

@router.put("/asset-categories/{category_id}", response_model=AssetCategoryResponse)
async def update_asset_category(
    category_id: int,
    category_update: AssetCategoryUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新资产分类"""
    try:
        # 检查分类是否存在
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == category_id))
        db_category = result.scalars().first()
        if not db_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{category_id}的资产分类不存在"
            )

        # 更新分类
        update_data = category_update.model_dump(exclude_unset=True)
        await db.execute(
            update(AssetCategory)
            .where(AssetCategory.id == category_id)
            .values(**update_data)
        )
        await db.commit()

        # 获取更新后的分类
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == category_id))
        updated_category = result.scalars().first()
        return updated_category
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新资产分类失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新资产分类失败: {str(e)}"
        )

@router.delete("/asset-categories/{category_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset_category(category_id: int, db: AsyncSession = Depends(get_db)):
    """删除资产分类"""
    try:
        # 检查分类是否存在
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == category_id))
        db_category = result.scalars().first()
        if not db_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{category_id}的资产分类不存在"
            )

        # 检查是否有资产使用此分类
        result = await db.execute(select(Asset).filter(Asset.category_id == category_id))
        if result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无法删除分类，因为有资产正在使用此分类"
            )

        # 删除分类
        await db.execute(delete(AssetCategory).where(AssetCategory.id == category_id))
        await db.commit()
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除资产分类失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资产分类失败: {str(e)}"
        )

# 资产API端点
@router.post("/assets")
async def create_asset(asset: AssetCreate, db: AsyncSession = Depends(get_db)):
    """创建新资产"""
    try:
        # 检查分类是否存在
        result = await db.execute(select(AssetCategory).filter(AssetCategory.id == asset.category_id))
        if not result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset.category_id}的资产分类不存在"
            )

        # 检查资产编码是否唯一
        result = await db.execute(select(Asset).filter(Asset.code == asset.code))
        if result.scalars().first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"资产编码'{asset.code}'已存在"
            )

        # 处理枚举类型和日期时区问题
        asset_data = asset.model_dump()

        # 处理枚举类型
        if isinstance(asset_data.get('lifecycle_status'), Enum):
            asset_data['lifecycle_status'] = asset_data['lifecycle_status'].value
        if isinstance(asset_data.get('depreciation_method'), Enum):
            asset_data['depreciation_method'] = asset_data['depreciation_method'].value

        # 处理日期时区问题
        if asset_data.get('purchase_date'):
            asset_data['purchase_date'] = asset_data['purchase_date'].replace(tzinfo=None)
        if asset_data.get('warranty_expire_date'):
            asset_data['warranty_expire_date'] = asset_data['warranty_expire_date'].replace(tzinfo=None)
        if asset_data.get('last_check_date'):
            asset_data['last_check_date'] = asset_data['last_check_date'].replace(tzinfo=None)

        # 创建资产
        db_asset = Asset(**asset_data)
        db.add(db_asset)
        await db.commit()
        await db.refresh(db_asset)
        return db_asset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建资产失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建资产失败: {str(e)}"
        )

@router.get("/assets")
async def get_assets(
    category_id: Optional[int] = None,
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取资产列表，可选按分类或状态筛选"""
    try:
        query = select(Asset)
        if category_id:
            query = query.filter(Asset.category_id == category_id)
        if status:
            query = query.filter(Asset.lifecycle_status == status)

        result = await db.execute(query)
        assets = result.scalars().all()
        return assets
    except Exception as e:
        logger.error(f"获取资产列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产列表失败: {str(e)}"
        )

@router.get("/assets/{asset_id}")
async def get_asset(asset_id: int, db: AsyncSession = Depends(get_db)):
    """获取特定资产详情"""
    try:
        result = await db.execute(select(Asset).filter(Asset.id == asset_id))
        asset = result.scalars().first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset_id}的资产不存在"
            )
        return asset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取资产详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取资产详情失败: {str(e)}"
        )

@router.put("/assets/{asset_id}")
async def update_asset(
    asset_id: int,
    asset_update: AssetUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新资产信息"""
    try:
        # 检查资产是否存在
        result = await db.execute(select(Asset).filter(Asset.id == asset_id))
        db_asset = result.scalars().first()
        if not db_asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset_id}的资产不存在"
            )

        # 更新资产
        update_data = asset_update.model_dump(exclude_unset=True)

        # 处理日期时区问题
        if update_data.get('purchase_date'):
            update_data['purchase_date'] = update_data['purchase_date'].replace(tzinfo=None)
        if update_data.get('warranty_expire_date'):
            update_data['warranty_expire_date'] = update_data['warranty_expire_date'].replace(tzinfo=None)
        if update_data.get('last_check_date'):
            update_data['last_check_date'] = update_data['last_check_date'].replace(tzinfo=None)

        await db.execute(
            update(Asset)
            .where(Asset.id == asset_id)
            .values(**update_data)
        )
        await db.commit()

        # 获取更新后的资产
        result = await db.execute(select(Asset).filter(Asset.id == asset_id))
        updated_asset = result.scalars().first()
        return updated_asset
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新资产失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新资产失败: {str(e)}"
        )

@router.delete("/assets/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_asset(asset_id: int, db: AsyncSession = Depends(get_db)):
    """删除资产"""
    try:
        # 检查资产是否存在
        result = await db.execute(select(Asset).filter(Asset.id == asset_id))
        db_asset = result.scalars().first()
        if not db_asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"ID为{asset_id}的资产不存在"
            )

        # 删除资产
        await db.execute(delete(Asset).where(Asset.id == asset_id))
        await db.commit()
        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除资产失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除资产失败: {str(e)}"
        )
