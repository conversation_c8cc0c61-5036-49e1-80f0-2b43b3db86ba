# Generated by Django 5.2.1 on 2025-05-20 18:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('device_monitor', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='InspectionTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模板名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('device_type', models.CharField(max_length=50, verbose_name='适用设备类型')),
                ('check_items', models.JSONField(verbose_name='检查项配置')),
                ('schedule', models.CharField(blank=True, max_length=50, verbose_name='定时表达式')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '巡检模板',
                'verbose_name_plural': '巡检模板',
            },
        ),
        migrations.CreateModel(
            name='InspectionTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', '待执行'), ('running', '执行中'), ('completed', '已完成'), ('failed', '执行失败')], default='pending', max_length=20, verbose_name='状态')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('devices', models.ManyToManyField(to='device_monitor.device')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='auto_inspection.inspectiontemplate')),
            ],
            options={
                'verbose_name': '巡检任务',
                'verbose_name_plural': '巡检任务',
            },
        ),
        migrations.CreateModel(
            name='InspectionResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('results', models.JSONField(verbose_name='检查结果')),
                ('is_normal', models.BooleanField(verbose_name='是否正常')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('device', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='device_monitor.device')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='auto_inspection.inspectiontask')),
            ],
            options={
                'verbose_name': '巡检结果',
                'verbose_name_plural': '巡检结果',
            },
        ),
    ]
