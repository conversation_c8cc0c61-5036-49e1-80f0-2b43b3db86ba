{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "燃石资产管理系统", "version": "2.0.0", "identifier": "com.burningrock.rs-asset", "build": {"frontendDist": "../out", "devUrl": "http://localhost:3000", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"label": "main", "title": "燃石资产管理系统", "width": 1400, "height": 900, "minWidth": 1000, "minHeight": 700, "resizable": true, "fullscreen": false, "center": true, "decorations": true, "transparent": false, "alwaysOnTop": false, "skipTaskbar": false, "shadow": true, "visible": true}], "security": {"csp": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https: wss: tauri:;", "devCsp": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https: wss: http://localhost:* ws://localhost:* tauri:;", "freezePrototype": false, "dangerousDisableAssetCspModification": false, "assetProtocol": {"enable": true, "scope": ["**"]}}, "trayIcon": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "copyright": "Copyright © 2024 Burning Rock. All rights reserved.", "category": "Productivity", "shortDescription": "燃石资产管理系统", "longDescription": "专业的企业级IT资产管理与监控系统，支持资产全生命周期管理、设备实时监控、网络管理等功能。", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": null}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": null}, "linux": {"deb": {"depends": []}}}, "plugins": {"fs": {"scope": ["$APPDATA/rs-asset", "$APPDATA/rs-asset/**", "$DOCUMENT", "$DOCUMENT/**", "$DESKTOP", "$DESKTOP/**", "$TEMP", "$TEMP/**"]}, "dialog": {"open": true, "save": true, "message": true, "ask": true, "confirm": true}, "http": {"scope": ["https://**", "http://localhost:*", "http://127.0.0.1:*"]}, "shell": {"scope": [{"name": "open-url", "cmd": "open", "args": ["$URL"]}, {"name": "open-file", "cmd": "explorer", "args": ["$FILE"]}]}, "notification": {"all": true}, "globalShortcut": {"all": false}, "os": {"all": true}, "process": {"all": false}, "log": {"level": "info"}}}