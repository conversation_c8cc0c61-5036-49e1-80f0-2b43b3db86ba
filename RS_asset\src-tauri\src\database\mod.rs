// 数据库模块
// 提供数据库连接、迁移、备份等功能

pub mod migrations;
pub mod seed;

use crate::error::{AppError, AppResult};
use sqlx::{sqlite::SqliteConnectOptions, SqlitePool, SqlitePoolOptions};
use std::path::Path;
use std::str::FromStr;

pub use migrations::MigrationManager;
pub use seed::SeedManager;

/// 数据库管理器
#[derive(Debug, Clone)]
pub struct DatabaseManager {
    pool: SqlitePool,
    db_path: String,
}

/// 数据库统计信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct DatabaseStats {
    pub total_assets: i64,
    pub total_categories: i64,
    pub total_users: i64,
    pub total_maintenance_orders: i64,
    pub total_spare_parts: i64,
    pub total_inspections: i64,
    pub db_size_bytes: u64,
    pub last_backup: Option<String>,
}

impl Default for DatabaseStats {
    fn default() -> Self {
        Self {
            total_assets: 0,
            total_categories: 0,
            total_users: 0,
            total_maintenance_orders: 0,
            total_spare_parts: 0,
            total_inspections: 0,
            db_size_bytes: 0,
            last_backup: None,
        }
    }
}

impl DatabaseManager {
    /// 初始化数据库管理器
    pub async fn new(db_path: &Path) -> AppResult<Self> {
        log::info!("初始化数据库: {}", db_path.display());

        // 确保数据库目录存在
        if let Some(parent) = db_path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| AppError::FileSystem {
                message: format!("创建数据库目录失败: {}", e),
            })?;
        }

        let db_url = format!("sqlite:{}", db_path.display());

        // 配置连接选项
        let connect_options = SqliteConnectOptions::from_str(&db_url)
            .map_err(|e| AppError::Database {
                message: format!("数据库连接选项配置失败: {}", e),
            })?
            .create_if_missing(true)
            .journal_mode(sqlx::sqlite::SqliteJournalMode::Wal)
            .synchronous(sqlx::sqlite::SqliteSynchronous::Normal)
            .busy_timeout(std::time::Duration::from_secs(30))
            .pragma("cache_size", "-64000") // 64MB cache
            .pragma("temp_store", "memory")
            .pragma("mmap_size", "268435456"); // 256MB mmap

        // 创建连接池
        let pool = SqlitePoolOptions::new()
            .max_connections(10)
            .min_connections(1)
            .acquire_timeout(std::time::Duration::from_secs(30))
            .idle_timeout(Some(std::time::Duration::from_secs(600)))
            .max_lifetime(Some(std::time::Duration::from_secs(1800)))
            .connect_with(connect_options)
            .await
            .map_err(|e| AppError::Database {
                message: format!("创建数据库连接池失败: {}", e),
            })?;

        let manager = Self {
            pool,
            db_path: db_path.to_string_lossy().to_string(),
        };

        // 运行数据库迁移
        manager.migrate().await?;

        log::info!("数据库初始化完成: {}", manager.db_path);
        Ok(manager)
    }

    /// 获取数据库连接池
    pub fn pool(&self) -> &SqlitePool {
        &self.pool
    }

    /// 获取数据库路径
    pub fn db_path(&self) -> &str {
        &self.db_path
    }

    /// 运行数据库迁移
    pub async fn migrate(&self) -> AppResult<()> {
        log::info!("开始数据库迁移...");

        let migration_manager = MigrationManager::new(self.pool.clone());
        migration_manager.run_migrations().await?;

        log::info!("数据库迁移完成");
        Ok(())
    }

    /// 获取数据库统计信息
    pub async fn get_stats(&self) -> AppResult<DatabaseStats> {
        let mut stats = DatabaseStats::default();

        // 获取各表的记录数
        let tables = vec![
            ("assets", &mut stats.total_assets),
            ("asset_categories", &mut stats.total_categories),
            ("users", &mut stats.total_users),
            ("maintenance_orders", &mut stats.total_maintenance_orders),
            ("spare_parts", &mut stats.total_spare_parts),
            ("inspection_records", &mut stats.total_inspections),
        ];

        for (table, count) in tables {
            let query = format!("SELECT COUNT(*) FROM {}", table);
            *count = sqlx::query_scalar::<_, i64>(&query)
                .fetch_one(&self.pool)
                .await
                .unwrap_or(0);
        }

        // 获取数据库文件大小
        if let Ok(metadata) = std::fs::metadata(&self.db_path) {
            stats.db_size_bytes = metadata.len();
        }

        // 获取最后备份时间
        if let Ok(backup_time) = sqlx::query_scalar::<_, String>(
            "SELECT value FROM configurations WHERE key = 'last_backup_time'"
        )
        .fetch_optional(&self.pool)
        .await
        {
            stats.last_backup = backup_time;
        }

        Ok(stats)
    }

    /// 备份数据库
    pub async fn backup(&self, backup_path: &Path) -> AppResult<()> {
        log::info!("开始备份数据库到: {}", backup_path.display());

        // 确保备份目录存在
        if let Some(parent) = backup_path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| AppError::FileSystem {
                message: format!("创建备份目录失败: {}", e),
            })?;
        }

        // 执行VACUUM INTO备份
        let backup_sql = format!("VACUUM INTO '{}'", backup_path.display());
        sqlx::query(&backup_sql)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("备份数据库失败: {}", e),
            })?;

        // 更新最后备份时间
        let now = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string();
        sqlx::query(
            "INSERT OR REPLACE INTO configurations (key, value, description, category) VALUES (?, ?, ?, ?)"
        )
        .bind("last_backup_time")
        .bind(&now)
        .bind("最后备份时间")
        .bind("backup")
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("更新备份时间失败: {}", e),
        })?;

        log::info!("数据库备份完成: {}", backup_path.display());
        Ok(())
    }

    /// 恢复数据库
    pub async fn restore(&self, backup_path: &Path) -> AppResult<()> {
        log::info!("开始从备份恢复数据库: {}", backup_path.display());

        if !backup_path.exists() {
            return Err(AppError::FileSystem {
                message: format!("备份文件不存在: {}", backup_path.display()),
            });
        }

        // 关闭当前连接池
        self.pool.close().await;

        // 复制备份文件到当前数据库位置
        std::fs::copy(backup_path, &self.db_path).map_err(|e| AppError::FileSystem {
            message: format!("恢复数据库文件失败: {}", e),
        })?;

        log::info!("数据库恢复完成");
        Ok(())
    }

    /// 优化数据库
    pub async fn optimize(&self) -> AppResult<()> {
        log::info!("开始优化数据库...");

        // 分析表统计信息
        sqlx::query("ANALYZE")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("分析数据库失败: {}", e),
            })?;

        // 重建索引
        sqlx::query("REINDEX")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("重建索引失败: {}", e),
            })?;

        // 执行VACUUM以回收空间
        sqlx::query("VACUUM")
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("VACUUM操作失败: {}", e),
            })?;

        log::info!("数据库优化完成");
        Ok(())
    }

    /// 检查数据库完整性
    pub async fn check_integrity(&self) -> AppResult<bool> {
        log::info!("检查数据库完整性...");

        let result = sqlx::query_scalar::<_, String>("PRAGMA integrity_check")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("完整性检查失败: {}", e),
            })?;

        let is_ok = result == "ok";
        if is_ok {
            log::info!("数据库完整性检查通过");
        } else {
            log::warn!("数据库完整性检查失败: {}", result);
        }

        Ok(is_ok)
    }

    /// 清理过期数据
    pub async fn cleanup_expired_data(&self) -> AppResult<()> {
        log::info!("开始清理过期数据...");

        // 获取数据保留天数配置
        let retention_days: i64 = sqlx::query_scalar(
            "SELECT value FROM configurations WHERE key = 'data_retention_days'"
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("获取数据保留配置失败: {}", e),
        })?
        .and_then(|v: String| v.parse().ok())
        .unwrap_or(90);

        // 清理过期的监控数据
        let cutoff_date = chrono::Utc::now() - chrono::Duration::days(retention_days);
        let cutoff_str = cutoff_date.format("%Y-%m-%d %H:%M:%S").to_string();

        let tables_to_clean = vec!["snmp_data", "network_data", "system_info"];

        for table in tables_to_clean {
            let query = format!("DELETE FROM {} WHERE timestamp < ?", table);
            let deleted = sqlx::query(&query)
                .bind(&cutoff_str)
                .execute(&self.pool)
                .await
                .map_err(|e| AppError::Database {
                    message: format!("清理表 {} 失败: {}", table, e),
                })?
                .rows_affected();

            log::info!("从表 {} 清理了 {} 条过期记录", table, deleted);
        }

        // 清理过期的审计日志
        let audit_query = "DELETE FROM audit_logs WHERE timestamp < ?";
        let deleted = sqlx::query(audit_query)
            .bind(&cutoff_str)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("清理审计日志失败: {}", e),
            })?
            .rows_affected();

        log::info!("清理了 {} 条过期审计日志", deleted);

        log::info!("过期数据清理完成");
        Ok(())
    }
}
