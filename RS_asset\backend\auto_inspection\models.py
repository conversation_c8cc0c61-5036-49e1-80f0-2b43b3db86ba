from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from device_monitor.models import Device

class InspectionTemplate(models.Model):
    """自动化巡检模板"""
    name = models.CharField('模板名称', max_length=100)
    description = models.TextField('描述', blank=True)
    device_type = models.CharField('适用设备类型', max_length=50)
    check_items = JSONField('检查项配置')
    schedule = models.CharField('定时表达式', max_length=50, blank=True)
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '巡检模板'
        verbose_name_plural = '巡检模板'

    def __str__(self):
        return self.name

class InspectionTask(models.Model):
    """巡检任务"""
    STATUS_CHOICES = [
        ('pending', '待执行'),
        ('running', '执行中'),
        ('completed', '已完成'),
        ('failed', '执行失败'),
    ]
    
    template = models.ForeignKey(InspectionTemplate, on_delete=models.CASCADE)
    devices = models.ManyToManyField(Device)
    status = models.CharField('状态', max_length=20, choices=STATUS_CHOICES, default='pending')
    started_at = models.DateTimeField('开始时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    created_by = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '巡检任务'
        verbose_name_plural = '巡检任务'

    def __str__(self):
        return f"{self.template.name} - {self.get_status_display()}"

class InspectionResult(models.Model):
    """巡检结果"""
    task = models.ForeignKey(InspectionTask, on_delete=models.CASCADE)
    device = models.ForeignKey(Device, on_delete=models.CASCADE)
    results = JSONField('检查结果')
    is_normal = models.BooleanField('是否正常')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)

    class Meta:
        verbose_name = '巡检结果'
        verbose_name_plural = '巡检结果'

    def __str__(self):
        return f"{self.device.name} - {'正常' if self.is_normal else '异常'}"
