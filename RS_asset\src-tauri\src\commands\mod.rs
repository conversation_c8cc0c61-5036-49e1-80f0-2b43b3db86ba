// 命令模块
// 定义所有 Tauri 命令的模块结构

pub mod asset;
pub mod monitoring;
pub mod network;
pub mod report;
pub mod system;
pub mod user;
pub mod file;
pub mod maintenance;
pub mod database;

// 重新导出所有命令
pub use asset::*;
pub use monitoring::*;
pub use network::*;
pub use report::*;
pub use system::*;
pub use user::*;
pub use file::*;
pub use maintenance::*;
pub use database::*;

use crate::error::{AppError, AppResult};
use crate::models::{PaginationParams, PaginationResponse};
use serde::{Deserialize, Serialize};

/// 通用响应结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommandResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<String>,
}

impl<T> CommandResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
        }
    }
    
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: Some(message),
            error: None,
        }
    }
    
    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
        }
    }
    
    pub fn from_result(result: AppResult<T>) -> Self {
        match result {
            Ok(data) => Self::success(data),
            Err(err) => Self::error(err.to_string()),
        }
    }
}

/// 空响应类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmptyResponse;

/// 健康检查响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckResponse {
    pub status: String,
    pub version: String,
    pub database_connected: bool,
    pub uptime_seconds: u64,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
    pub timestamp: chrono::NaiveDateTime,
}

/// 应用信息响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppInfoResponse {
    pub name: String,
    pub version: String,
    pub description: String,
    pub author: String,
    pub build_date: String,
    pub git_commit: Option<String>,
    pub rust_version: String,
    pub tauri_version: String,
}

/// 通用健康检查命令
#[tauri::command]
pub async fn health_check() -> CommandResponse<HealthCheckResponse> {
    let start_time = std::time::SystemTime::now();
    let uptime = start_time
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    
    // 获取系统信息
    let mut sys = sysinfo::System::new_all();
    sys.refresh_all();
    
    let memory_usage_mb = (sys.used_memory() as f64) / 1024.0 / 1024.0;
    let cpu_usage_percent = sys.global_cpu_info().cpu_usage() as f64;
    
    let response = HealthCheckResponse {
        status: "healthy".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        database_connected: true, // TODO: 实际检查数据库连接
        uptime_seconds: uptime,
        memory_usage_mb,
        cpu_usage_percent,
        timestamp: chrono::Utc::now().naive_utc(),
    };
    
    CommandResponse::success(response)
}

/// 获取应用信息命令
#[tauri::command]
pub async fn get_app_info() -> CommandResponse<AppInfoResponse> {
    let response = AppInfoResponse {
        name: env!("CARGO_PKG_NAME").to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        description: env!("CARGO_PKG_DESCRIPTION").to_string(),
        author: env!("CARGO_PKG_AUTHORS").to_string(),
        build_date: env!("BUILD_DATE").unwrap_or("unknown").to_string(),
        git_commit: option_env!("GIT_COMMIT").map(|s| s.to_string()),
        rust_version: env!("RUST_VERSION").unwrap_or("unknown").to_string(),
        tauri_version: "2.0.0".to_string(),
    };
    
    CommandResponse::success(response)
}

/// 通用错误处理宏
#[macro_export]
macro_rules! handle_command {
    ($expr:expr) => {
        match $expr {
            Ok(result) => CommandResponse::success(result),
            Err(err) => {
                log::error!("Command error: {}", err);
                CommandResponse::error(err.to_string())
            }
        }
    };
}

/// 分页查询宏
#[macro_export]
macro_rules! paginated_query {
    ($query:expr, $params:expr, $total_query:expr) => {
        {
            let offset = ($params.page - 1) * $params.page_size;
            let items = $query
                .limit($params.page_size as i64)
                .offset(offset as i64)
                .fetch_all(&db)
                .await?;
            
            let total: i64 = $total_query.fetch_one(&db).await?.0;
            let total_pages = (total as f64 / $params.page_size as f64).ceil() as i32;
            
            PaginationResponse {
                items,
                total,
                page: $params.page,
                page_size: $params.page_size,
                total_pages,
                has_next: $params.page < total_pages,
                has_prev: $params.page > 1,
            }
        }
    };
}

/// 验证分页参数
pub fn validate_pagination(params: &PaginationParams) -> AppResult<()> {
    if params.page < 1 {
        return Err(AppError::Validation("页码必须大于0".to_string()));
    }
    
    if params.page_size < 1 || params.page_size > 1000 {
        return Err(AppError::Validation("每页大小必须在1-1000之间".to_string()));
    }
    
    Ok(())
}

/// 验证ID参数
pub fn validate_id(id: i64, name: &str) -> AppResult<()> {
    if id <= 0 {
        return Err(AppError::Validation(format!("{} ID必须大于0", name)));
    }
    Ok(())
}

/// 验证字符串参数
pub fn validate_string(value: &str, name: &str, min_len: usize, max_len: usize) -> AppResult<()> {
    let trimmed = value.trim();
    if trimmed.is_empty() {
        return Err(AppError::Validation(format!("{}不能为空", name)));
    }
    
    if trimmed.len() < min_len || trimmed.len() > max_len {
        return Err(AppError::Validation(
            format!("{}长度必须在{}-{}个字符之间", name, min_len, max_len)
        ));
    }
    
    Ok(())
}

/// 验证邮箱格式
pub fn validate_email(email: &str) -> AppResult<()> {
    let email = email.trim();
    if email.is_empty() {
        return Err(AppError::Validation("邮箱不能为空".to_string()));
    }
    
    if !email.contains('@') || !email.contains('.') {
        return Err(AppError::Validation("邮箱格式不正确".to_string()));
    }
    
    // 简单的邮箱格式验证
    let parts: Vec<&str> = email.split('@').collect();
    if parts.len() != 2 || parts[0].is_empty() || parts[1].is_empty() {
        return Err(AppError::Validation("邮箱格式不正确".to_string()));
    }
    
    if !parts[1].contains('.') {
        return Err(AppError::Validation("邮箱格式不正确".to_string()));
    }
    
    Ok(())
}

/// 验证IP地址格式
pub fn validate_ip_address(ip: &str) -> AppResult<()> {
    use std::net::IpAddr;
    
    ip.parse::<IpAddr>()
        .map_err(|_| AppError::Validation("IP地址格式不正确".to_string()))?;
    
    Ok(())
}

/// 验证端口号
pub fn validate_port(port: u16) -> AppResult<()> {
    if port == 0 {
        return Err(AppError::Validation("端口号不能为0".to_string()));
    }
    Ok(())
}

/// 验证MAC地址格式
pub fn validate_mac_address(mac: &str) -> AppResult<()> {
    let mac = mac.trim().to_lowercase();
    
    // 支持两种格式: xx:xx:xx:xx:xx:xx 或 xx-xx-xx-xx-xx-xx
    let parts: Vec<&str> = if mac.contains(':') {
        mac.split(':').collect()
    } else if mac.contains('-') {
        mac.split('-').collect()
    } else {
        return Err(AppError::Validation("MAC地址格式不正确".to_string()));
    };
    
    if parts.len() != 6 {
        return Err(AppError::Validation("MAC地址格式不正确".to_string()));
    }
    
    for part in parts {
        if part.len() != 2 {
            return Err(AppError::Validation("MAC地址格式不正确".to_string()));
        }
        
        if !part.chars().all(|c| c.is_ascii_hexdigit()) {
            return Err(AppError::Validation("MAC地址格式不正确".to_string()));
        }
    }
    
    Ok(())
}

/// 验证日期范围
pub fn validate_date_range(
    start_date: Option<chrono::NaiveDateTime>,
    end_date: Option<chrono::NaiveDateTime>,
) -> AppResult<()> {
    if let (Some(start), Some(end)) = (start_date, end_date) {
        if start >= end {
            return Err(AppError::Validation("开始日期必须早于结束日期".to_string()));
        }
        
        let now = chrono::Utc::now().naive_utc();
        if start > now {
            return Err(AppError::Validation("开始日期不能是未来时间".to_string()));
        }
    }
    
    Ok(())
}

/// 安全地转换字符串为数字
pub fn safe_parse_number<T: std::str::FromStr>(value: &str, name: &str) -> AppResult<T> {
    value.trim().parse::<T>()
        .map_err(|_| AppError::Validation(format!("{}格式不正确", name)))
}

/// 生成随机字符串
pub fn generate_random_string(length: usize) -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                            abcdefghijklmnopqrstuvwxyz\
                            0123456789";
    let mut rng = rand::thread_rng();
    
    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

/// 格式化文件大小
pub fn format_file_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB", "PB"];
    
    if bytes == 0 {
        return "0 B".to_string();
    }
    
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}

/// 格式化持续时间
pub fn format_duration(seconds: u64) -> String {
    let days = seconds / 86400;
    let hours = (seconds % 86400) / 3600;
    let minutes = (seconds % 3600) / 60;
    let secs = seconds % 60;
    
    if days > 0 {
        format!("{}天{}小时{}分钟", days, hours, minutes)
    } else if hours > 0 {
        format!("{}小时{}分钟", hours, minutes)
    } else if minutes > 0 {
        format!("{}分钟{}秒", minutes, secs)
    } else {
        format!("{}秒", secs)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_validate_email() {
        assert!(validate_email("<EMAIL>").is_ok());
        assert!(validate_email("<EMAIL>").is_ok());
        assert!(validate_email("invalid-email").is_err());
        assert!(validate_email("@domain.com").is_err());
        assert!(validate_email("user@").is_err());
    }
    
    #[test]
    fn test_validate_ip_address() {
        assert!(validate_ip_address("***********").is_ok());
        assert!(validate_ip_address("::1").is_ok());
        assert!(validate_ip_address("invalid-ip").is_err());
        assert!(validate_ip_address("256.256.256.256").is_err());
    }
    
    #[test]
    fn test_validate_mac_address() {
        assert!(validate_mac_address("00:11:22:33:44:55").is_ok());
        assert!(validate_mac_address("00-11-22-33-44-55").is_ok());
        assert!(validate_mac_address("AA:BB:CC:DD:EE:FF").is_ok());
        assert!(validate_mac_address("invalid-mac").is_err());
        assert!(validate_mac_address("00:11:22:33:44").is_err());
    }
    
    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(0), "0 B");
        assert_eq!(format_file_size(1024), "1.00 KB");
        assert_eq!(format_file_size(1048576), "1.00 MB");
        assert_eq!(format_file_size(1073741824), "1.00 GB");
    }
    
    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(30), "30秒");
        assert_eq!(format_duration(90), "1分钟30秒");
        assert_eq!(format_duration(3661), "1小时1分钟");
        assert_eq!(format_duration(90061), "1天1小时1分钟");
    }
    
    #[test]
    fn test_generate_random_string() {
        let random_str = generate_random_string(10);
        assert_eq!(random_str.len(), 10);
        assert!(random_str.chars().all(|c| c.is_alphanumeric()));
    }
}