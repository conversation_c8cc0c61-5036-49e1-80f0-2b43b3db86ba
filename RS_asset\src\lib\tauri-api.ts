import { invoke } from '@tauri-apps/api/core';
import { open, save } from '@tauri-apps/plugin-dialog';
import { writeTextFile, readTextFile } from '@tauri-apps/plugin-fs';
import { sendNotification } from '@tauri-apps/plugin-notification';

// 资产管理API
export const assetApi = {
  // 获取所有资产
  async getAssets() {
    try {
      return await invoke('get_assets');
    } catch (error) {
      console.error('获取资产列表失败:', error);
      throw error;
    }
  },

  // 创建资产
  async createAsset(asset: any) {
    try {
      return await invoke('create_asset', { asset });
    } catch (error) {
      console.error('创建资产失败:', error);
      throw error;
    }
  },

  // 更新资产
  async updateAsset(id: string, asset: any) {
    try {
      return await invoke('update_asset', { id, asset });
    } catch (error) {
      console.error('更新资产失败:', error);
      throw error;
    }
  },

  // 删除资产
  async deleteAsset(id: string) {
    try {
      return await invoke('delete_asset', { id });
    } catch (error) {
      console.error('删除资产失败:', error);
      throw error;
    }
  }
};

// 监控API
export const monitoringApi = {
  // 获取设备状态
  async getDeviceStatus() {
    try {
      return await invoke('get_device_status');
    } catch (error) {
      console.error('获取设备状态失败:', error);
      throw error;
    }
  },

  // 获取网络监控数据
  async getNetworkData() {
    try {
      return await invoke('get_network_data');
    } catch (error) {
      console.error('获取网络数据失败:', error);
      throw error;
    }
  }
};

// 通用HTTP请求函数（用于外部API调用）
export async function httpRequest(url: string, options: RequestInit = {}) {
  try {
    return await invoke('http_request', { url, options });
  } catch (error) {
    console.error('HTTP请求失败:', error);
    throw error;
  }
}

// 文件操作API
export const fileApi = {
  // 导出数据
  async exportData(data: any, filename?: string) {
    try {
      const filePath = await save({
        defaultPath: filename || 'export.json',
        filters: [
          { name: 'JSON', extensions: ['json'] },
          { name: 'CSV', extensions: ['csv'] },
          { name: 'Excel', extensions: ['xlsx'] }
        ]
      });

      if (filePath) {
        await writeTextFile(filePath, JSON.stringify(data, null, 2));
        await sendNotification({
          title: '导出成功',
          body: `数据已导出到 ${filePath}`
        });
        return filePath;
      }
    } catch (error) {
      console.error('导出数据失败:', error);
      throw error;
    }
  },

  // 导入数据
  async importData() {
    try {
      const filePath = await open({
        multiple: false,
        filters: [
          { name: 'JSON', extensions: ['json'] },
          { name: 'CSV', extensions: ['csv'] },
          { name: 'Excel', extensions: ['xlsx'] }
        ]
      });

      if (filePath) {
        const content = await readTextFile(filePath as string);
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('导入数据失败:', error);
      throw error;
    }
  }
};