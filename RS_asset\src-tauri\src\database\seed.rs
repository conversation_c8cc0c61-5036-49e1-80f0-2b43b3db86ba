// 数据库种子数据模块
// 用于生成测试和演示数据

use crate::error::{AppError, AppResult};
use sqlx::SqlitePool;
use uuid::Uuid;

/// 数据库种子数据管理器
pub struct SeedManager {
    pool: SqlitePool,
}

impl SeedManager {
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    /// 生成所有种子数据
    pub async fn seed_all(&self) -> AppResult<()> {
        log::info!("开始生成种子数据...");

        self.seed_asset_categories().await?;
        self.seed_users().await?;
        self.seed_roles().await?;
        self.seed_assets().await?;
        self.seed_spare_parts().await?;
        self.seed_maintenance_orders().await?;
        self.seed_inspection_plans().await?;

        log::info!("种子数据生成完成");
        Ok(())
    }

    /// 生成资产分类数据
    async fn seed_asset_categories(&self) -> AppResult<()> {
        let categories = vec![
            ("网络设备", "网络基础设施设备", None, "network", "#4169E1"),
            ("服务器", "计算服务器设备", None, "server", "#41B883"),
            ("存储设备", "数据存储设备", None, "storage", "#F7BA1E"),
            ("安防设备", "安全防护设备", None, "security", "#E34D59"),
            ("办公设备", "日常办公设备", None, "office", "#9C27B0"),
            ("路由器", "网络路由设备", Some(1), "router", "#4169E1"),
            ("交换机", "网络交换设备", Some(1), "switch", "#4169E1"),
            ("防火墙", "网络安全设备", Some(1), "firewall", "#E34D59"),
            ("物理服务器", "物理计算服务器", Some(2), "physical-server", "#41B883"),
            ("虚拟服务器", "虚拟化服务器", Some(2), "virtual-server", "#41B883"),
        ];

        for (name, description, parent_id, icon, color) in categories {
            sqlx::query(
                r#"
                INSERT OR IGNORE INTO asset_categories 
                (name, description, parent_id, icon, color, is_active) 
                VALUES (?, ?, ?, ?, ?, TRUE)
                "#
            )
            .bind(name)
            .bind(description)
            .bind(parent_id)
            .bind(icon)
            .bind(color)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入资产分类失败: {}", e),
            })?;
        }

        Ok(())
    }

    /// 生成用户数据
    async fn seed_users(&self) -> AppResult<()> {
        let users = vec![
            ("admin", "<EMAIL>", "系统管理员", "A001", "系统管理员", "IT部门"),
            ("manager", "<EMAIL>", "部门经理", "M001", "部门经理", "运维部门"),
            ("tech1", "<EMAIL>", "技术员1", "T001", "高级技术员", "运维部门"),
            ("tech2", "<EMAIL>", "技术员2", "T002", "技术员", "运维部门"),
            ("operator", "<EMAIL>", "操作员", "O001", "设备操作员", "生产部门"),
        ];

        for (username, email, full_name, employee_id, position, department) in users {
            let password_hash = bcrypt::hash("123456", bcrypt::DEFAULT_COST)
                .map_err(|e| AppError::Internal {
                    message: format!("密码哈希失败: {}", e),
                })?;

            sqlx::query(
                r#"
                INSERT OR IGNORE INTO users 
                (username, email, password_hash, full_name, employee_id, position, department, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, ?, TRUE)
                "#
            )
            .bind(username)
            .bind(email)
            .bind(password_hash)
            .bind(full_name)
            .bind(employee_id)
            .bind(position)
            .bind(department)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入用户失败: {}", e),
            })?;
        }

        Ok(())
    }

    /// 生成角色数据
    async fn seed_roles(&self) -> AppResult<()> {
        // 为用户分配角色
        let user_roles = vec![
            ("admin", "admin"),
            ("manager", "manager"),
            ("tech1", "technician"),
            ("tech2", "technician"),
            ("operator", "operator"),
        ];

        for (username, role_name) in user_roles {
            sqlx::query(
                r#"
                INSERT OR IGNORE INTO user_roles (user_id, role_id) 
                SELECT u.id, r.id FROM users u, roles r 
                WHERE u.username = ? AND r.name = ?
                "#
            )
            .bind(username)
            .bind(role_name)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("分配用户角色失败: {}", e),
            })?;
        }

        Ok(())
    }

    /// 生成资产数据
    async fn seed_assets(&self) -> AppResult<()> {
        let assets = vec![
            ("核心路由器-01", "RT-001", "华为", "AR6300", "SN001", "数据中心", "网络设备", "192.168.1.1"),
            ("核心交换机-01", "SW-001", "华为", "S5700", "SN002", "数据中心", "网络设备", "192.168.1.2"),
            ("防火墙-01", "FW-001", "华为", "USG6000", "SN003", "数据中心", "安防设备", "192.168.1.3"),
            ("应用服务器-01", "SV-001", "戴尔", "PowerEdge R740", "SN004", "数据中心", "服务器", "192.168.1.10"),
            ("数据库服务器-01", "SV-002", "戴尔", "PowerEdge R750", "SN005", "数据中心", "服务器", "************"),
            ("存储阵列-01", "ST-001", "EMC", "VNX5400", "SN006", "数据中心", "存储设备", "************"),
            ("UPS电源-01", "UP-001", "APC", "Smart-UPS 3000", "SN007", "数据中心", "办公设备", ""),
            ("监控摄像头-01", "CM-001", "海康威视", "DS-2CD2T47G1", "SN008", "大厅", "安防设备", "*************"),
        ];

        for (name, asset_code, brand, model, serial_number, location, category, ip_address) in assets {
            let qr_code = format!("QR-{}", Uuid::new_v4().to_string()[..8].to_uppercase());
            
            sqlx::query(
                r#"
                INSERT OR IGNORE INTO assets 
                (name, asset_code, qr_code, brand, model, serial_number, location, ip_address, 
                 category_id, asset_status, lifecycle_status, purchase_date, purchase_price, 
                 responsible_person, created_at) 
                SELECT ?, ?, ?, ?, ?, ?, ?, ?, 
                       ac.id, 'available', 'in_use', '2023-01-01', 50000.00, 
                       '系统管理员', CURRENT_TIMESTAMP
                FROM asset_categories ac WHERE ac.name = ?
                "#
            )
            .bind(name)
            .bind(asset_code)
            .bind(qr_code)
            .bind(brand)
            .bind(model)
            .bind(serial_number)
            .bind(location)
            .bind(ip_address)
            .bind(category)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入资产失败: {}", e),
            })?;
        }

        Ok(())
    }

    /// 生成备件数据
    async fn seed_spare_parts(&self) -> AppResult<()> {
        let spare_parts = vec![
            ("PWR-001", "电源模块", "服务器电源模块", "电源", "华为", "500W", "个", 1200.00, 2, 10, 5),
            ("FAN-001", "散热风扇", "服务器散热风扇", "散热", "戴尔", "120mm", "个", 150.00, 5, 20, 12),
            ("MEM-001", "内存条", "DDR4 32GB内存条", "内存", "三星", "32GB DDR4", "条", 2500.00, 3, 15, 8),
            ("HDD-001", "硬盘", "企业级硬盘", "存储", "希捷", "2TB SAS", "块", 800.00, 5, 25, 15),
            ("CBL-001", "网线", "超五类网线", "网络", "安普", "Cat5e", "米", 5.00, 100, 500, 200),
        ];

        for (part_number, name, description, category, brand, specification, unit, unit_price, min_stock, max_stock, current_stock) in spare_parts {
            sqlx::query(
                r#"
                INSERT OR IGNORE INTO spare_parts 
                (part_number, name, description, category, brand, specification, unit, 
                 unit_price, min_stock_level, max_stock_level, current_stock, location, 
                 supplier, is_critical, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, '仓库A', '华为技术有限公司', ?, CURRENT_TIMESTAMP)
                "#
            )
            .bind(part_number)
            .bind(name)
            .bind(description)
            .bind(category)
            .bind(brand)
            .bind(specification)
            .bind(unit)
            .bind(unit_price)
            .bind(min_stock)
            .bind(max_stock)
            .bind(current_stock)
            .bind(category == "电源" || category == "内存") // 关键备件
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入备件失败: {}", e),
            })?;
        }

        Ok(())
    }

    /// 生成维修工单数据
    async fn seed_maintenance_orders(&self) -> AppResult<()> {
        let orders = vec![
            ("WO-001", "服务器风扇异常", "应用服务器-01风扇噪音异常，需要检查", "high", "pending", "corrective"),
            ("WO-002", "网络连接不稳定", "核心交换机-01端口连接不稳定", "medium", "in_progress", "corrective"),
            ("WO-003", "定期维护", "数据库服务器-01季度定期维护", "low", "scheduled", "preventive"),
        ];

        for (order_number, title, description, priority, status, order_type) in orders {
            sqlx::query(
                r#"
                INSERT OR IGNORE INTO maintenance_orders 
                (order_number, title, description, priority, status, order_type, 
                 asset_id, reported_by, assigned_to, estimated_hours, created_at) 
                SELECT ?, ?, ?, ?, ?, ?, 
                       a.id, u1.id, u2.id, 4.0, CURRENT_TIMESTAMP
                FROM assets a, users u1, users u2 
                WHERE a.asset_code = 'SV-001' AND u1.username = 'admin' AND u2.username = 'tech1'
                LIMIT 1
                "#
            )
            .bind(order_number)
            .bind(title)
            .bind(description)
            .bind(priority)
            .bind(status)
            .bind(order_type)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入维修工单失败: {}", e),
            })?;
        }

        Ok(())
    }

    /// 生成巡检计划数据
    async fn seed_inspection_plans(&self) -> AppResult<()> {
        let plans = vec![
            ("数据中心日常巡检", "数据中心设备日常巡检", "daily", 1),
            ("网络设备周检", "网络设备周度检查", "weekly", 1),
            ("服务器月检", "服务器月度深度检查", "monthly", 1),
        ];

        for (name, description, frequency_type, frequency_value) in plans {
            sqlx::query(
                r#"
                INSERT OR IGNORE INTO inspection_plans 
                (name, description, frequency_type, frequency_value, assigned_to, 
                 is_active, next_inspection_date, created_at) 
                SELECT ?, ?, ?, ?, u.id, TRUE, DATE('now', '+1 day'), CURRENT_TIMESTAMP
                FROM users u WHERE u.username = 'tech1'
                "#
            )
            .bind(name)
            .bind(description)
            .bind(frequency_type)
            .bind(frequency_value)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("插入巡检计划失败: {}", e),
            })?;
        }

        Ok(())
    }

    /// 清除所有种子数据
    pub async fn clear_seed_data(&self) -> AppResult<()> {
        log::info!("开始清除种子数据...");

        let tables = vec![
            "inspection_records",
            "inspection_plans", 
            "maintenance_order_status_logs",
            "maintenance_orders",
            "spare_part_transactions",
            "spare_parts",
            "assets",
            "user_roles",
            "users",
            "asset_categories",
        ];

        for table in tables {
            let query = format!("DELETE FROM {} WHERE id > 0", table);
            sqlx::query(&query)
                .execute(&self.pool)
                .await
                .map_err(|e| AppError::Database {
                    message: format!("清除表 {} 数据失败: {}", table, e),
                })?;
        }

        log::info!("种子数据清除完成");
        Ok(())
    }
}
