# 设备资产管理系统 - UI设计原型

## 一、设计规范

### 1. 色彩系统

#### 主色调
- **主色**：#4169E1（皇家蓝）- 用于主要按钮、导航栏、重要信息高亮
- **辅助色**：#41B883（绿色）- 用于成功状态、正常运行指示
- **警示色**：#E34D59（红色）- 用于错误提示、异常状态、报警信息
- **提醒色**：#F7BA1E（黄色）- 用于警告、待处理状态

#### 中性色
- 背景色：#F5F7FA（浅灰色）- 页面背景
- 文字色：#1A1A1A（深灰色）- 主要文字
- 边框色：#DCDFE6（淡灰色）- 分割线、边框

### 2. 字体规范

- 标题：18px, 600 weight
- 副标题：16px, 500 weight
- 正文：14px, 400 weight
- 辅助文字：12px, 400 weight

### 3. 图标系统

- 线性图标，统一使用2px描边
- 功能图标尺寸：24px × 24px
- 导航图标尺寸：20px × 20px
- 状态图标尺寸：16px × 16px

### 4. 响应式设计

- PC端：最小宽度1280px，适配1920px宽屏
- 平板：768px - 1024px
- 移动端：320px - 480px

## 二、组件设计

### 1. 导航组件

#### PC端侧边导航
- 固定在左侧，宽度220px
- 包含系统Logo、主导航菜单、用户信息
- 支持折叠模式（宽度减少到64px，只显示图标）
- 二级菜单通过悬浮展开

#### 移动端导航
- 顶部固定导航栏，高度56px
- 左侧抽屉式菜单，点击汉堡按钮展开
- 底部TabBar，包含：首页、设备、工单、我的

### 2. 卡片组件

- 圆角：8px
- 阴影：0 2px 12px 0 rgba(0, 0, 0, 0.1)
- 内边距：16px
- 标题区高度：48px，底部边框分隔
- 内容区根据实际内容自适应

### 3. 表格组件

- 表头背景色：#F2F6FC
- 分割线：1px solid #EBEEF5
- 行高：48px
- 悬浮高亮：背景色变为#F5F7FA
- 分页控件位于表格底部右侧

### 4. 表单组件

- 输入框高度：36px
- 标签位置：左对齐，宽度120px
- 必填项标记：红色星号
- 校验错误提示：位于输入框下方，红色文字

### 5. 按钮组件

- 主要按钮：蓝色背景，白色文字
- 次要按钮：白色背景，蓝色边框和文字
- 危险按钮：红色背景或边框
- 按钮高度：36px（默认）、32px（小）、40px（大）
- 按钮圆角：4px

### 6. 状态标签

- 正常：绿色背景，#E1F3D8底色，#41B883文字
- 异常：红色背景，#FCE2E2底色，#E34D59文字
- 待处理：黄色背景，#FDF6EC底色，#F7BA1E文字
- 处理中：蓝色背景，#E8F4FF底色，#4169E1文字

## 三、页面设计

### 1. 登录页

- 左侧品牌区：展示系统名称、Logo和背景图
- 右侧登录表单：包含用户名、密码输入框和登录按钮
- 底部版权信息和帮助链接
- 响应式设计：移动端时品牌区隐藏，只显示表单区

### 2. 首页/仪表盘

- 顶部数据卡片：展示设备总数、故障数、待处理工单数等关键指标
- 中部图表区：设备状态分布饼图、近期工单趋势折线图
- 底部列表区：最近工单、待处理任务列表
- 右侧边栏：通知消息、待办事项

### 3. 设备台账页

#### PC端设计
- 顶部搜索和筛选区：支持按设备名称、编号、状态等筛选
- 右上角功能按钮：新增、导入、导出
- 中部表格：展示设备列表，包含编号、名称、型号、状态等字段
- 操作列：查看详情、编辑、删除等操作按钮
- 支持表格列自定义显示

#### 移动端设计
- 顶部搜索栏
- 设备卡片列表：每张卡片展示设备基本信息和状态标签
- 下拉刷新、上拉加载更多
- 右下角悬浮按钮：扫码功能

### 4. 设备详情页

- 顶部设备基本信息卡片：包含名称、编号、状态等
- 右上角二维码图标：点击显示设备二维码
- 标签页导航：基本信息、3D模型、运行数据、维修记录、点检记录
- 基本信息标签页：详细的设备参数和属性
- 3D模型标签页：Three.js渲染的设备3D模型，支持旋转、缩放
- 运行数据标签页：实时数据曲线图，支持历史数据查询
- 维修记录标签页：设备维修历史列表
- 点检记录标签页：设备点检历史列表

### 5. 维修工单页

#### 工单列表
- 左侧筛选面板：按状态、时间、优先级等筛选
- 中部工单卡片：拖拽式看板，分为待处理、处理中、已完成三列
- 每张卡片包含：工单号、设备信息、故障描述、优先级标签、创建时间
- 右侧详情面板：点击卡片后显示工单详情

#### 工单详情
- 顶部基本信息：工单号、创建时间、状态等
- 设备信息区：关联设备的基本信息和快速链接
- 故障信息区：故障描述、故障等级、图片附件
- 处理记录区：时间轴形式展示处理过程和操作记录
- 底部操作区：根据当前状态显示不同的操作按钮（接单、转派、完成等）

### 6. 点检巡检页

#### PC端任务管理
- 左侧日历视图：展示当月任务分布
- 右侧任务列表：按日期分组显示任务
- 任务卡片包含：设备信息、巡检路线、截止时间、执行状态
- 顶部统计图表：完成率、异常发现率等指标

#### 移动端巡检执行
- 任务列表页：显示待执行的巡检任务
- 任务详情页：显示巡检点和检查项
- 扫码确认页：扫描设备二维码确认身份
- 巡检执行页：检查项列表，支持拍照、录音、选择结果
- 异常上报页：发现异常时填写详情并创建工单

### 7. 备品备件管理页

- 顶部库存概览：库存总量、预警数量等指标
- 中部备件列表：展示备件名称、编号、库存量、最低库存等信息
- 库存状态标签：正常（绿色）、预警（黄色）、紧缺（红色）
- 右侧操作区：入库、出库、调拨等操作按钮
- 底部关联设备列表：显示使用该备件的设备

### 8. 数据分析页

- 顶部时间筛选器：选择分析时间范围
- 设备效率分析：OEE指标图表，包含可用率、性能率、质量率
- 故障分析：故障类型分布饼图、故障趋势折线图
- 维修分析：平均修复时间、维修成本统计
- 能耗分析：能耗趋势图、能耗异常设备排名
- 支持数据导出和报表生成

## 四、交互设计

### 1. 扫码交互

#### 设备扫码查看
1. 点击移动端底部扫码按钮
2. 调用摄像头扫描设备二维码
3. 识别成功后直接跳转到设备详情页
4. 支持离线扫码，数据同步后显示完整信息

#### 巡检扫码确认
1. 进入巡检任务详情页
2. 点击"开始巡检"按钮
3. 扫描设备二维码确认位置
4. 显示巡检检查项列表
5. 完成检查后提交结果

### 2. 工单流转

#### 报修流程
1. 用户发现设备异常，创建报修工单
2. 填写设备信息（可通过扫码快速选择）
3. 填写故障描述，上传故障图片
4. 选择故障等级，提交工单
5. 系统通知相关维修人员

#### 维修流程
1. 维修人员接收工单通知
2. 查看工单详情，接单处理
3. 现场维修，记录维修过程
4. 使用备件时扫码出库
5. 完成维修后填写处理结果
6. 提交工单，等待验收

### 3. 3D模型交互

1. 进入设备详情页，切换到3D模型标签
2. 模型自动加载并旋转展示
3. 支持手势操作：双指缩放、单指旋转
4. 点击模型特定部位，显示部件详情
5. 支持爆炸视图展示，了解内部结构

### 4. 数据可视化交互

1. 实时数据曲线支持时间范围选择
2. 图表支持缩放、平移操作
3. 鼠标悬停显示详细数据值
4. 异常点高亮显示，点击查看详情
5. 支持多指标对比分析

## 五、移动端适配

### 1. 响应式布局策略

- 使用Flex布局和Grid布局实现响应式
- 关键数据优先展示原则
- 复杂表格在移动端转为卡片列表
- 操作按钮在移动端合并为下拉菜单

### 2. 触控优化

- 按钮尺寸不小于44px×44px，确保可触达性
- 列表项高度增加，便于手指点击
- 添加触摸反馈效果
- 支持常见手势操作：滑动、双击、长按等

### 3. 离线功能支持

- 巡检任务支持离线执行，联网后自动同步
- 常用设备信息本地缓存，离线也可查看基本信息
- 表单数据本地保存，防止意外丢失

## 六、原型页面示例

### 1. 登录页

```
+------------------------------------------+
|                                          |
|  +----------------+  +----------------+  |
|  |                |  | 设备资产管理系统 |  |
|  |                |  +----------------+  |
|  |                |                      |
|  |   系统Logo      |  +----------------+  |
|  |   & 背景图     |  | 用户名          |  |
|  |                |  +----------------+  |
|  |                |                      |
|  |                |  +----------------+  |
|  |                |  | 密码           |  |
|  |                |  +----------------+  |
|  |                |                      |
|  |                |  +----------------+  |
|  |                |  |     登录       |  |
|  |                |  +----------------+  |
|  +----------------+                      |
|                                          |
+------------------------------------------+
```

### 2. 设备台账列表页

```
+------------------------------------------+
| 设备资产管理系统                  用户名 ▼ |
+----------+-----------------------------+
|          |  设备台账                   |
|          |                            |
|  导航菜单  |  +------------------------+ |
|          |  | 搜索框    | 筛选 | 新增 | |
|  首页     |  +------------------------+ |
|          |                            |
|  设备台账  |  +------------------------+ |
|          |  | 设备编号 | 名称 | 状态.. | |
|  维修工单  |  +------------------------+ |
|          |  | DEV0079 | 泵.. | 正常   | |
|  点检巡检  |  +------------------------+ |
|          |  | DEV0047 | BCC | 故障   | |
|  备件管理  |  +------------------------+ |
|          |  | DEV0038 | 2#.. | 待检  | |
|  数据分析  |  +------------------------+ |
|          |  | DEV3006 | 碳.. | 正常   | |
|          |  +------------------------+ |
|          |  |      分页控件          | |
+----------+-----------------------------+
```

### 3. 设备详情页

```
+------------------------------------------+
| < 返回                            扫码 🔍 |
+------------------------------------------+
| 设备名称：碳酸铜压滤机                    |
| 设备编号：DEV3006    状态：正常           |
+------------------------------------------+
| 基本信息 | 3D模型 | 运行数据 | 维修记录    |
+------------------------------------------+
|                                          |
| 设备类型：压滤机                          |
| 规格型号：XMY30/630                      |
| 所属部门：物化车间                        |
| 负责人：李超明                            |
| 启用时间：2021-6-15                      |
|                                          |
| 设备参数                                 |
| +----------------+---------------------+ |
| | 参数名称       | 参数值              | |
| +----------------+---------------------+ |
| | 额定功率       | 15kW               | |
| | 工作压力       | 0.8MPa             | |
| | 过滤面积       | 30m²               | |
| +----------------+---------------------+ |
|                                          |
+------------------------------------------+
```

### 4. 移动端设备列表

```
+------------------+
| 设备台账     🔍  |
+------------------+
| 筛选 ▼            |
+------------------+
| +----------------+ |
| | DEV0079       | |
| | 破碎提升泵     | |
| | 正常  查看 >   | |
| +----------------+ |
|                    |
| +----------------+ |
| | DEV0047       | |
| | BCC压滤机      | |
| | 故障  查看 >   | |
| +----------------+ |
|                    |
| +----------------+ |
| | DEV0038       | |
| | 2#离心机       | |
| | 待检  查看 >   | |
| +----------------+ |
|                    |
|        📷         |
+------------------+
```

### 5. 工单处理看板

```
+------------------------------------------+
| 维修工单                           新增 + |
+------------------------------------------+
| 待处理 (3)  | 处理中 (5)  | 已完成 (12)   |
+------------------------------------------+
|              |               |            |
| +-----------+ | +-----------+ |           |
| | #WO220612 | | | #WO220610 | |           |
| | 泵轴断裂   | | | 电机过热  | |           |
| | 紧急 DEV79 | | | 高 DEV47  | |           |
| +-----------+ | +-----------+ |           |
|              |               |            |
| +-----------+ | +-----------+ |           |
| | #WO220611 | | | #WO220609 | |           |
| | 漏油      | | | 阀门卡死   | |           |
| | 中 DEV38  | | | 中 DEV30  | |           |
| +-----------+ | +-----------+ |           |
|              |               |            |
| +-----------+ | +-----------+ |           |
| | #WO220608 | | | #WO220607 | |           |
| | 噪音异常   | | | 轴承磨损   | |           |
| | 低 DEV45  | | | 高 DEV28  | |           |
| +-----------+ | +-----------+ |           |
+------------------------------------------+
```

## 七、设计交付物

1. **设计规范文档**：包含色彩、字体、组件规范
2. **组件库**：基于Tailwind CSS的UI组件库
3. **页面原型**：所有核心页面的高保真设计
4. **交互原型**：关键流程的可交互原型
5. **响应式设计规范**：不同设备的适配方案
6. **图标资源包**：系统所需的所有图标资源
7. **设计标注**：提供给前端开发的详细标注

## 八、实现建议

1. **使用Tailwind CSS实现**：利用Tailwind的原子类快速构建UI
2. **组件化开发**：将设计拆分为可复用组件
3. **响应式优先**：从移动端开始设计，逐步扩展到大屏
4. **性能优化**：大数据表格使用虚拟滚动，3D模型按需加载
5. **主题定制**：基于设计规范定制Tailwind配置
6. **无障碍设计**：符合WCAG 2.1 AA级标准
7. **动效适度**：添加适当的过渡动画提升体验，但不过度使用