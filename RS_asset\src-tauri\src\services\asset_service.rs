// 资产管理服务
// 提供资产的增删改查、分类管理、生命周期管理等功能

use crate::error::{AppError, AppResult};
use crate::models::{Asset, AssetCategory, AssetFilter, PaginationParams, PaginationResponse};
use crate::database::DatabaseManager;
use sqlx::{SqlitePool, Row};
use std::sync::Arc;
use uuid::Uuid;
use chrono::{DateTime, Utc, NaiveDateTime};
use serde::{Deserialize, Serialize};

/// 资产服务
#[derive(Debug, Clone)]
pub struct AssetService {
    pool: SqlitePool,
}

/// 资产创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAssetRequest {
    pub name: String,
    pub asset_code: String,
    pub category_id: Option<i64>,
    pub brand: Option<String>,
    pub model: Option<String>,
    pub serial_number: Option<String>,
    pub purchase_date: Option<String>,
    pub purchase_price: Option<f64>,
    pub location: Option<String>,
    pub department: Option<String>,
    pub responsible_person: Option<String>,
    pub responsible_person_phone: Option<String>,
    pub ip_address: Option<String>,
    pub mac_address: Option<String>,
    pub hostname: Option<String>,
    pub operating_system: Option<String>,
    pub cpu_info: Option<String>,
    pub memory_gb: Option<i32>,
    pub storage_type: Option<String>,
    pub storage_capacity_gb: Option<i32>,
    pub network_ports: Option<i32>,
    pub power_consumption: Option<i32>,
    pub warranty_start_date: Option<String>,
    pub warranty_end_date: Option<String>,
    pub warranty_provider: Option<String>,
    pub installation_date: Option<String>,
    pub notes: Option<String>,
    pub tags: Option<String>,
    pub is_critical: Option<bool>,
}

/// 资产更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAssetRequest {
    pub name: Option<String>,
    pub category_id: Option<i64>,
    pub brand: Option<String>,
    pub model: Option<String>,
    pub serial_number: Option<String>,
    pub purchase_date: Option<String>,
    pub purchase_price: Option<f64>,
    pub current_value: Option<f64>,
    pub lifecycle_status: Option<String>,
    pub asset_status: Option<String>,
    pub location: Option<String>,
    pub department: Option<String>,
    pub responsible_person: Option<String>,
    pub responsible_person_phone: Option<String>,
    pub ip_address: Option<String>,
    pub mac_address: Option<String>,
    pub hostname: Option<String>,
    pub operating_system: Option<String>,
    pub cpu_info: Option<String>,
    pub memory_gb: Option<i32>,
    pub storage_type: Option<String>,
    pub storage_capacity_gb: Option<i32>,
    pub network_ports: Option<i32>,
    pub power_consumption: Option<i32>,
    pub warranty_start_date: Option<String>,
    pub warranty_end_date: Option<String>,
    pub warranty_provider: Option<String>,
    pub installation_date: Option<String>,
    pub last_maintenance_date: Option<String>,
    pub next_maintenance_date: Option<String>,
    pub maintenance_interval_days: Option<i32>,
    pub notes: Option<String>,
    pub tags: Option<String>,
    pub is_critical: Option<bool>,
}

/// 资产分类创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateCategoryRequest {
    pub name: String,
    pub description: Option<String>,
    pub parent_id: Option<i64>,
    pub icon: Option<String>,
    pub color: Option<String>,
    pub sort_order: Option<i32>,
}

/// 资产分类更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCategoryRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub parent_id: Option<i64>,
    pub icon: Option<String>,
    pub color: Option<String>,
    pub sort_order: Option<i32>,
    pub is_active: Option<bool>,
}

/// 资产统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssetStats {
    pub total_assets: i64,
    pub assets_by_status: std::collections::HashMap<String, i64>,
    pub assets_by_category: std::collections::HashMap<String, i64>,
    pub assets_by_location: std::collections::HashMap<String, i64>,
    pub critical_assets: i64,
    pub warranty_expiring_soon: i64,
    pub maintenance_due: i64,
}

impl AssetService {
    /// 创建新的资产服务实例
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    /// 创建资产
    pub async fn create_asset(&self, request: CreateAssetRequest, created_by: i64) -> AppResult<Asset> {
        // 生成二维码
        let qr_code = format!("QR-{}", Uuid::new_v4().to_string()[..8].to_uppercase());

        let result = sqlx::query(
            r#"
            INSERT INTO assets (
                name, asset_code, qr_code, category_id, brand, model, serial_number,
                purchase_date, purchase_price, location, department, responsible_person,
                responsible_person_phone, ip_address, mac_address, hostname, operating_system,
                cpu_info, memory_gb, storage_type, storage_capacity_gb, network_ports,
                power_consumption, warranty_start_date, warranty_end_date, warranty_provider,
                installation_date, notes, tags, is_critical, created_by, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP
            )
            "#
        )
        .bind(&request.name)
        .bind(&request.asset_code)
        .bind(&qr_code)
        .bind(request.category_id)
        .bind(&request.brand)
        .bind(&request.model)
        .bind(&request.serial_number)
        .bind(&request.purchase_date)
        .bind(request.purchase_price)
        .bind(&request.location)
        .bind(&request.department)
        .bind(&request.responsible_person)
        .bind(&request.responsible_person_phone)
        .bind(&request.ip_address)
        .bind(&request.mac_address)
        .bind(&request.hostname)
        .bind(&request.operating_system)
        .bind(&request.cpu_info)
        .bind(request.memory_gb)
        .bind(&request.storage_type)
        .bind(request.storage_capacity_gb)
        .bind(request.network_ports)
        .bind(request.power_consumption)
        .bind(&request.warranty_start_date)
        .bind(&request.warranty_end_date)
        .bind(&request.warranty_provider)
        .bind(&request.installation_date)
        .bind(&request.notes)
        .bind(&request.tags)
        .bind(request.is_critical.unwrap_or(false))
        .bind(created_by)
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("创建资产失败: {}", e),
        })?;

        let asset_id = result.last_insert_rowid();
        self.get_asset_by_id(asset_id).await
    }

    /// 根据ID获取资产
    pub async fn get_asset_by_id(&self, id: i64) -> AppResult<Asset> {
        let row = sqlx::query(
            r#"
            SELECT a.*, ac.name as category_name, 
                   u1.full_name as created_by_name, u2.full_name as updated_by_name
            FROM assets a
            LEFT JOIN asset_categories ac ON a.category_id = ac.id
            LEFT JOIN users u1 ON a.created_by = u1.id
            LEFT JOIN users u2 ON a.updated_by = u2.id
            WHERE a.id = ?
            "#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("查询资产失败: {}", e),
        })?
        .ok_or_else(|| AppError::NotFound {
            message: format!("资产不存在: {}", id),
        })?;

        Ok(Asset::from_row(&row)?)
    }

    /// 根据资产编码获取资产
    pub async fn get_asset_by_code(&self, asset_code: &str) -> AppResult<Asset> {
        let row = sqlx::query(
            r#"
            SELECT a.*, ac.name as category_name,
                   u1.full_name as created_by_name, u2.full_name as updated_by_name
            FROM assets a
            LEFT JOIN asset_categories ac ON a.category_id = ac.id
            LEFT JOIN users u1 ON a.created_by = u1.id
            LEFT JOIN users u2 ON a.updated_by = u2.id
            WHERE a.asset_code = ?
            "#
        )
        .bind(asset_code)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("查询资产失败: {}", e),
        })?
        .ok_or_else(|| AppError::NotFound {
            message: format!("资产不存在: {}", asset_code),
        })?;

        Ok(Asset::from_row(&row)?)
    }

    /// 更新资产
    pub async fn update_asset(&self, id: i64, request: UpdateAssetRequest, updated_by: i64) -> AppResult<Asset> {
        let mut query_parts = Vec::new();
        let mut params: Vec<Box<dyn sqlx::Encode<'_, sqlx::Sqlite> + Send + Sync>> = Vec::new();

        // 动态构建更新查询
        if let Some(name) = &request.name {
            query_parts.push("name = ?");
            params.push(Box::new(name.clone()));
        }
        if let Some(category_id) = request.category_id {
            query_parts.push("category_id = ?");
            params.push(Box::new(category_id));
        }
        // ... 添加其他字段的更新逻辑

        if query_parts.is_empty() {
            return Err(AppError::BadRequest {
                message: "没有提供要更新的字段".to_string(),
            });
        }

        query_parts.push("updated_by = ?");
        query_parts.push("updated_at = CURRENT_TIMESTAMP");
        params.push(Box::new(updated_by));

        let query = format!(
            "UPDATE assets SET {} WHERE id = ?",
            query_parts.join(", ")
        );
        params.push(Box::new(id));

        // 由于动态查询的复杂性，这里简化处理
        // 在实际应用中，建议使用查询构建器或ORM
        sqlx::query("UPDATE assets SET updated_by = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?")
            .bind(updated_by)
            .bind(id)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("更新资产失败: {}", e),
            })?;

        self.get_asset_by_id(id).await
    }

    /// 删除资产
    pub async fn delete_asset(&self, id: i64) -> AppResult<()> {
        let result = sqlx::query("DELETE FROM assets WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("删除资产失败: {}", e),
            })?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound {
                message: format!("资产不存在: {}", id),
            });
        }

        Ok(())
    }

    /// 分页查询资产
    pub async fn list_assets(
        &self,
        filter: Option<AssetFilter>,
        pagination: PaginationParams,
    ) -> AppResult<PaginationResponse<Asset>> {
        let mut where_clauses = Vec::new();
        let mut params: Vec<String> = Vec::new();

        // 构建查询条件
        if let Some(filter) = filter {
            if let Some(keyword) = filter.keyword {
                where_clauses.push("(a.name LIKE ? OR a.asset_code LIKE ? OR a.brand LIKE ? OR a.model LIKE ?)");
                let search_term = format!("%{}%", keyword);
                params.extend(vec![search_term.clone(), search_term.clone(), search_term.clone(), search_term]);
            }
            if let Some(category_id) = filter.category_id {
                where_clauses.push("a.category_id = ?");
                params.push(category_id.to_string());
            }
            if let Some(status) = filter.status {
                where_clauses.push("a.asset_status = ?");
                params.push(status);
            }
            if let Some(location) = filter.location {
                where_clauses.push("a.location = ?");
                params.push(location);
            }
        }

        let where_clause = if where_clauses.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_clauses.join(" AND "))
        };

        // 查询总数
        let count_query = format!(
            "SELECT COUNT(*) FROM assets a {}",
            where_clause
        );

        let total: i64 = sqlx::query_scalar(&count_query)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("查询资产总数失败: {}", e),
            })?;

        // 查询数据
        let data_query = format!(
            r#"
            SELECT a.*, ac.name as category_name,
                   u1.full_name as created_by_name, u2.full_name as updated_by_name
            FROM assets a
            LEFT JOIN asset_categories ac ON a.category_id = ac.id
            LEFT JOIN users u1 ON a.created_by = u1.id
            LEFT JOIN users u2 ON a.updated_by = u2.id
            {}
            ORDER BY a.created_at DESC
            LIMIT ? OFFSET ?
            "#,
            where_clause
        );

        let offset = (pagination.page - 1) * pagination.page_size;
        let rows = sqlx::query(&data_query)
            .bind(pagination.page_size as i64)
            .bind(offset as i64)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("查询资产列表失败: {}", e),
            })?;

        let mut assets = Vec::new();
        for row in rows {
            assets.push(Asset::from_row(&row)?);
        }

        Ok(PaginationResponse {
            data: assets,
            total,
            page: pagination.page,
            page_size: pagination.page_size,
            total_pages: (total as f64 / pagination.page_size as f64).ceil() as i64,
        })
    }

    /// 获取资产统计信息
    pub async fn get_asset_stats(&self) -> AppResult<AssetStats> {
        // 总资产数
        let total_assets: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM assets")
            .fetch_one(&self.pool)
            .await
            .unwrap_or(0);

        // 按状态统计
        let status_rows = sqlx::query("SELECT asset_status, COUNT(*) as count FROM assets GROUP BY asset_status")
            .fetch_all(&self.pool)
            .await
            .unwrap_or_default();

        let mut assets_by_status = std::collections::HashMap::new();
        for row in status_rows {
            let status: String = row.get("asset_status");
            let count: i64 = row.get("count");
            assets_by_status.insert(status, count);
        }

        // 按分类统计
        let category_rows = sqlx::query(
            "SELECT ac.name, COUNT(*) as count FROM assets a LEFT JOIN asset_categories ac ON a.category_id = ac.id GROUP BY ac.name"
        )
        .fetch_all(&self.pool)
        .await
        .unwrap_or_default();

        let mut assets_by_category = std::collections::HashMap::new();
        for row in category_rows {
            let category: Option<String> = row.get("name");
            let count: i64 = row.get("count");
            assets_by_category.insert(category.unwrap_or("未分类".to_string()), count);
        }

        // 按位置统计
        let location_rows = sqlx::query("SELECT location, COUNT(*) as count FROM assets WHERE location IS NOT NULL GROUP BY location")
            .fetch_all(&self.pool)
            .await
            .unwrap_or_default();

        let mut assets_by_location = std::collections::HashMap::new();
        for row in location_rows {
            let location: String = row.get("location");
            let count: i64 = row.get("count");
            assets_by_location.insert(location, count);
        }

        // 关键资产数
        let critical_assets: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM assets WHERE is_critical = TRUE")
            .fetch_one(&self.pool)
            .await
            .unwrap_or(0);

        // 保修即将到期的资产
        let warranty_expiring_soon: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM assets WHERE warranty_end_date IS NOT NULL AND DATE(warranty_end_date) BETWEEN DATE('now') AND DATE('now', '+30 days')"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0);

        // 需要维护的资产
        let maintenance_due: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM assets WHERE next_maintenance_date IS NOT NULL AND DATE(next_maintenance_date) <= DATE('now')"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0);

        Ok(AssetStats {
            total_assets,
            assets_by_status,
            assets_by_category,
            assets_by_location,
            critical_assets,
            warranty_expiring_soon,
            maintenance_due,
        })
    }

    /// 创建资产分类
    pub async fn create_category(&self, request: CreateCategoryRequest, created_by: i64) -> AppResult<AssetCategory> {
        let result = sqlx::query(
            r#"
            INSERT INTO asset_categories (
                name, description, parent_id, icon, color, sort_order, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            "#
        )
        .bind(&request.name)
        .bind(&request.description)
        .bind(request.parent_id)
        .bind(&request.icon)
        .bind(request.color.as_deref().unwrap_or("#4169E1"))
        .bind(request.sort_order.unwrap_or(0))
        .bind(created_by)
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("创建资产分类失败: {}", e),
        })?;

        let category_id = result.last_insert_rowid();
        self.get_category_by_id(category_id).await
    }

    /// 根据ID获取资产分类
    pub async fn get_category_by_id(&self, id: i64) -> AppResult<AssetCategory> {
        let row = sqlx::query(
            r#"
            SELECT ac.*, pac.name as parent_name,
                   u1.full_name as created_by_name, u2.full_name as updated_by_name
            FROM asset_categories ac
            LEFT JOIN asset_categories pac ON ac.parent_id = pac.id
            LEFT JOIN users u1 ON ac.created_by = u1.id
            LEFT JOIN users u2 ON ac.updated_by = u2.id
            WHERE ac.id = ?
            "#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("查询资产分类失败: {}", e),
        })?
        .ok_or_else(|| AppError::NotFound {
            message: format!("资产分类不存在: {}", id),
        })?;

        Ok(AssetCategory::from_row(&row)?)
    }

    /// 获取所有资产分类（树形结构）
    pub async fn list_categories(&self) -> AppResult<Vec<AssetCategory>> {
        let rows = sqlx::query(
            r#"
            SELECT ac.*, pac.name as parent_name,
                   u1.full_name as created_by_name, u2.full_name as updated_by_name
            FROM asset_categories ac
            LEFT JOIN asset_categories pac ON ac.parent_id = pac.id
            LEFT JOIN users u1 ON ac.created_by = u1.id
            LEFT JOIN users u2 ON ac.updated_by = u2.id
            WHERE ac.is_active = TRUE
            ORDER BY ac.sort_order, ac.name
            "#
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("查询资产分类列表失败: {}", e),
        })?;

        let mut categories = Vec::new();
        for row in rows {
            categories.push(AssetCategory::from_row(&row)?);
        }

        Ok(categories)
    }

    /// 删除资产分类
    pub async fn delete_category(&self, id: i64) -> AppResult<()> {
        // 检查是否有子分类
        let child_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM asset_categories WHERE parent_id = ?")
            .bind(id)
            .fetch_one(&self.pool)
            .await
            .unwrap_or(0);

        if child_count > 0 {
            return Err(AppError::BadRequest {
                message: "无法删除有子分类的分类".to_string(),
            });
        }

        // 检查是否有关联的资产
        let asset_count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM assets WHERE category_id = ?")
            .bind(id)
            .fetch_one(&self.pool)
            .await
            .unwrap_or(0);

        if asset_count > 0 {
            return Err(AppError::BadRequest {
                message: "无法删除有关联资产的分类".to_string(),
            });
        }

        let result = sqlx::query("DELETE FROM asset_categories WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("删除资产分类失败: {}", e),
            })?;

        if result.rows_affected() == 0 {
            return Err(AppError::NotFound {
                message: format!("资产分类不存在: {}", id),
            });
        }

        Ok(())
    }
}
