// 数据库迁移模块
// 管理数据库版本和结构变更

use crate::error::{AppError, AppResult};
use sqlx::SqlitePool;
use std::collections::HashMap;

/// 数据库迁移管理器
pub struct MigrationManager {
    pool: SqlitePool,
}

/// 迁移脚本定义
#[derive(Debug, Clone)]
pub struct Migration {
    pub version: String,
    pub description: String,
    pub up_sql: String,
    pub down_sql: String,
}

impl MigrationManager {
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    /// 运行所有待执行的迁移
    pub async fn run_migrations(&self) -> AppResult<()> {
        log::info!("开始检查数据库迁移...");

        // 确保迁移表存在
        self.ensure_migration_table().await?;

        // 获取所有迁移脚本
        let migrations = self.get_all_migrations();

        // 获取已执行的迁移
        let executed_migrations = self.get_executed_migrations().await?;

        // 执行未执行的迁移
        for migration in migrations {
            if !executed_migrations.contains(&migration.version) {
                log::info!("执行迁移: {} - {}", migration.version, migration.description);
                self.execute_migration(&migration).await?;
            }
        }

        log::info!("数据库迁移检查完成");
        Ok(())
    }

    /// 确保迁移表存在
    async fn ensure_migration_table(&self) -> AppResult<()> {
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            "#,
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("创建迁移表失败: {}", e),
        })?;

        Ok(())
    }

    /// 获取已执行的迁移版本
    async fn get_executed_migrations(&self) -> AppResult<Vec<String>> {
        let rows = sqlx::query_scalar::<_, String>("SELECT version FROM schema_migrations ORDER BY version")
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("查询已执行迁移失败: {}", e),
            })?;

        Ok(rows)
    }

    /// 执行单个迁移
    async fn execute_migration(&self, migration: &Migration) -> AppResult<()> {
        let mut tx = self.pool.begin().await.map_err(|e| AppError::Database {
            message: format!("开始事务失败: {}", e),
        })?;

        // 执行迁移SQL
        sqlx::query(&migration.up_sql)
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::Database {
                message: format!("执行迁移SQL失败: {}", e),
            })?;

        // 记录迁移
        sqlx::query(
            "INSERT INTO schema_migrations (version, description) VALUES (?, ?)"
        )
        .bind(&migration.version)
        .bind(&migration.description)
        .execute(&mut *tx)
        .await
        .map_err(|e| AppError::Database {
            message: format!("记录迁移失败: {}", e),
        })?;

        tx.commit().await.map_err(|e| AppError::Database {
            message: format!("提交事务失败: {}", e),
        })?;

        Ok(())
    }

    /// 回滚迁移
    pub async fn rollback_migration(&self, version: &str) -> AppResult<()> {
        log::info!("回滚迁移: {}", version);

        let migrations = self.get_all_migrations();
        let migration = migrations
            .iter()
            .find(|m| m.version == version)
            .ok_or_else(|| AppError::Database {
                message: format!("未找到迁移版本: {}", version),
            })?;

        let mut tx = self.pool.begin().await.map_err(|e| AppError::Database {
            message: format!("开始事务失败: {}", e),
        })?;

        // 执行回滚SQL
        sqlx::query(&migration.down_sql)
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::Database {
                message: format!("执行回滚SQL失败: {}", e),
            })?;

        // 删除迁移记录
        sqlx::query("DELETE FROM schema_migrations WHERE version = ?")
            .bind(version)
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::Database {
                message: format!("删除迁移记录失败: {}", e),
            })?;

        tx.commit().await.map_err(|e| AppError::Database {
            message: format!("提交事务失败: {}", e),
        })?;

        log::info!("迁移回滚完成: {}", version);
        Ok(())
    }

    /// 获取所有迁移脚本
    fn get_all_migrations(&self) -> Vec<Migration> {
        vec![
            Migration {
                version: "20250101_000001".to_string(),
                description: "初始化数据库结构".to_string(),
                up_sql: "-- 此迁移由主数据库初始化函数处理".to_string(),
                down_sql: "-- 不支持回滚初始化".to_string(),
            },
            Migration {
                version: "20250101_000002".to_string(),
                description: "添加资产二维码字段".to_string(),
                up_sql: r#"
                    ALTER TABLE assets ADD COLUMN qr_code TEXT UNIQUE;
                    CREATE INDEX IF NOT EXISTS idx_assets_qr_code ON assets(qr_code);
                "#.to_string(),
                down_sql: r#"
                    DROP INDEX IF EXISTS idx_assets_qr_code;
                    ALTER TABLE assets DROP COLUMN qr_code;
                "#.to_string(),
            },
            Migration {
                version: "20250101_000003".to_string(),
                description: "添加用户头像和偏好设置".to_string(),
                up_sql: r#"
                    ALTER TABLE users ADD COLUMN avatar_url TEXT;
                    ALTER TABLE users ADD COLUMN preferences TEXT;
                "#.to_string(),
                down_sql: r#"
                    ALTER TABLE users DROP COLUMN avatar_url;
                    ALTER TABLE users DROP COLUMN preferences;
                "#.to_string(),
            },
            Migration {
                version: "20250101_000004".to_string(),
                description: "添加维修工单附件字段".to_string(),
                up_sql: r#"
                    ALTER TABLE maintenance_orders ADD COLUMN attachments TEXT;
                "#.to_string(),
                down_sql: r#"
                    ALTER TABLE maintenance_orders DROP COLUMN attachments;
                "#.to_string(),
            },
            Migration {
                version: "20250101_000005".to_string(),
                description: "添加备件图片字段".to_string(),
                up_sql: r#"
                    ALTER TABLE spare_parts ADD COLUMN image_urls TEXT;
                "#.to_string(),
                down_sql: r#"
                    ALTER TABLE spare_parts DROP COLUMN image_urls;
                "#.to_string(),
            },
        ]
    }

    /// 获取迁移状态
    pub async fn get_migration_status(&self) -> AppResult<HashMap<String, bool>> {
        let all_migrations = self.get_all_migrations();
        let executed_migrations = self.get_executed_migrations().await?;

        let mut status = HashMap::new();
        for migration in all_migrations {
            status.insert(
                migration.version.clone(),
                executed_migrations.contains(&migration.version),
            );
        }

        Ok(status)
    }

    /// 检查数据库是否需要迁移
    pub async fn needs_migration(&self) -> AppResult<bool> {
        let all_migrations = self.get_all_migrations();
        let executed_migrations = self.get_executed_migrations().await?;

        Ok(all_migrations.len() > executed_migrations.len())
    }
}
