from rest_framework import serializers
from .models import InspectionTemplate, InspectionTask, InspectionResult

class InspectionTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = InspectionTemplate
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class InspectionTaskSerializer(serializers.ModelSerializer):
    template_name = serializers.CharField(source='template.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = InspectionTask
        fields = '__all__'
        read_only_fields = ('created_at', 'started_at', 'completed_at')

class InspectionResultSerializer(serializers.ModelSerializer):
    device_name = serializers.CharField(source='device.name', read_only=True)
    
    class Meta:
        model = InspectionResult
        fields = '__all__'
        read_only_fields = ('created_at',)
