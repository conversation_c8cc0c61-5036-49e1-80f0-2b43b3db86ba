from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from typing import List, Dict, Optional, Any
from datetime import datetime
import json

from database import get_db
from services.snmp_collection_service import get_snmp_collection_service
from pydantic import BaseModel, Field

router = APIRouter(prefix="/api/snmp-config", tags=["snmp-config"])

# 采集频率配置模型
class CollectionIntervals(BaseModel):
    environment: int = Field(60, description="环境监控设备采集间隔(秒)")
    ups: int = Field(60, description="UPS设备采集间隔(秒)")
    mains: int = Field(60, description="市电监控设备采集间隔(秒)")
    network: int = Field(300, description="网络设备采集间隔(秒)")
    printer: int = Field(900, description="打印机设备采集间隔(秒)")
    default: int = Field(300, description="默认采集间隔(秒)")


# 数据保留配置模型
class DataRetention(BaseModel):
    environment: int = Field(90, description="环境监控数据保留天数")
    ups: int = Field(90, description="UPS数据保留天数")
    mains: int = Field(90, description="市电监控数据保留天数")
    network: int = Field(30, description="网络设备数据保留天数")
    printer: int = Field(30, description="打印机数据保留天数")
    custom: int = Field(30, description="自定义SNMP数据保留天数")


# 启用状态配置模型
class EnabledTypes(BaseModel):
    environment: bool = Field(True, description="环境监控设备采集启用状态")
    ups: bool = Field(True, description="UPS设备采集启用状态")
    mains: bool = Field(True, description="市电监控设备采集启用状态")
    network: bool = Field(True, description="网络设备采集启用状态")
    printer: bool = Field(True, description="打印机设备采集启用状态")
    custom: bool = Field(True, description="自定义SNMP数据采集启用状态")


# SNMP采集配置模型
class SNMPCollectionConfig(BaseModel):
    collection_intervals: CollectionIntervals
    data_retention: DataRetention
    enabled_types: EnabledTypes


# 数据统计模型
class DataStats(BaseModel):
    environment_count: int
    ups_count: int
    mains_count: int
    network_count: int
    printer_count: int
    custom_count: int
    total_count: int
    last_30_days_count: int


# 获取SNMP采集配置
@router.get("/collection", response_model=SNMPCollectionConfig)
async def get_collection_config():
    """获取SNMP采集配置"""
    try:
        # 获取SNMP采集服务实例
        service = get_snmp_collection_service()
        
        # 返回配置
        return {
            "collection_intervals": {
                "environment": service.polling_intervals.get("environment", 60),
                "ups": service.polling_intervals.get("ups", 60),
                "mains": service.polling_intervals.get("mains", 60),
                "network": service.polling_intervals.get("network", 300),
                "printer": service.polling_intervals.get("printer", 900),
                "default": service.polling_intervals.get("default", 300)
            },
            "data_retention": {
                "environment": 90,  # 这里应该从配置中读取
                "ups": 90,
                "mains": 90,
                "network": 30,
                "printer": 30,
                "custom": 30
            },
            "enabled_types": {
                "environment": True,  # 这里应该从配置中读取
                "ups": True,
                "mains": True,
                "network": True,
                "printer": True,
                "custom": True
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取SNMP采集配置失败: {str(e)}")


# 更新SNMP采集配置
@router.post("/collection", response_model=SNMPCollectionConfig)
async def update_collection_config(config: SNMPCollectionConfig):
    """更新SNMP采集配置"""
    try:
        # 获取SNMP采集服务实例
        service = get_snmp_collection_service()
        
        # 更新采集频率
        service.polling_intervals = {
            "environment": config.collection_intervals.environment,
            "ups": config.collection_intervals.ups,
            "mains": config.collection_intervals.mains,
            "network": config.collection_intervals.network,
            "printer": config.collection_intervals.printer,
            "default": config.collection_intervals.default
        }
        
        # 这里应该保存数据保留配置和启用状态配置
        
        # 返回更新后的配置
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新SNMP采集配置失败: {str(e)}")


# 获取数据统计
@router.get("/stats", response_model=DataStats)
async def get_data_stats(db: AsyncSession = Depends(get_db)):
    """获取SNMP数据统计"""
    try:
        # 这里应该从数据库中查询各类型数据的统计信息
        # 暂时返回模拟数据
        return {
            "environment_count": 12458,
            "ups_count": 8721,
            "mains_count": 4567,
            "network_count": 5342,
            "printer_count": 3210,
            "custom_count": 9876,
            "total_count": 44174,
            "last_30_days_count": 12345
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据统计失败: {str(e)}")


# 手动触发数据清理
@router.post("/cleanup")
async def trigger_data_cleanup(db: AsyncSession = Depends(get_db)):
    """手动触发数据清理"""
    try:
        # 这里应该实现数据清理逻辑
        # 暂时返回成功
        return {"message": "数据清理任务已触发"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发数据清理失败: {str(e)}")


@router.get("/status")
async def get_collection_status():
    """获取SNMP采集服务状态和设备采集状态"""
    try:
        service = get_snmp_collection_service()
        return {
            "service_status": "running" if service.is_running else "stopped",
            "devices": [
                {
                    "device_id": device_id,
                    "status": "active",
                    "last_collected": service.last_collection_time.get(device_id),
                    "type": connector.device_type
                }
                for device_id, connector in service.connectors.items()
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取采集状态失败: {str(e)}")

# 手动触发数据采集
@router.post("/collect/{device_type}")
async def trigger_data_collection(device_type: str):
    """手动触发数据采集"""
    try:
        # 获取SNMP采集服务实例
        service = get_snmp_collection_service()
        
        # 验证设备类型
        valid_types = ["environment", "ups", "mains", "network", "printer", "all"]
        if device_type not in valid_types:
            raise HTTPException(status_code=400, detail=f"无效的设备类型: {device_type}")
        
        # 这里应该实现手动触发数据采集的逻辑
        # 暂时返回成功
        return {"message": f"{device_type} 设备数据采集任务已触发"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发数据采集失败: {str(e)}")
