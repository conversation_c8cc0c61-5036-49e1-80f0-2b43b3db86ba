// Tauri 2.0 燃石资产管理系统
// 主要库文件，定义应用程序的核心结构和功能

use tauri::{Manager, State};
use std::sync::Arc;
use tokio::sync::Mutex;
use log::{info, error, warn};

// 导入模块
mod database;
mod models;
mod commands;
mod services;
mod utils;
mod error;

// 重新导出主要类型
pub use database::Database;
pub use error::{AppError, AppResult};
pub use models::*;

// 应用状态
#[derive(Debug)]
pub struct AppState {
    pub database: Arc<Database>,
    pub config: Arc<utils::config::AppConfig>,
}

impl AppState {
    pub async fn new() -> AppResult<Self> {
        info!("初始化应用状态...");
        
        // 加载配置
        let config = Arc::new(utils::config::AppConfig::load()?);
        info!("配置加载完成");
        
        // 初始化数据库
        let database = Arc::new(Database::new(&config.database_url).await?);
        info!("数据库连接建立");
        
        // 运行数据库迁移
        database.migrate().await?;
        info!("数据库迁移完成");
        
        Ok(Self {
            database,
            config,
        })
    }
}

// Tauri 应用程序入口点
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 初始化日志系统
    env_logger::Builder::from_default_env()
        .filter_level(log::LevelFilter::Info)
        .init();
    
    info!("启动燃石资产管理系统 v2.0.0");
    
    tauri::Builder::default()
        .plugin(tauri_plugin_log::Builder::default().build())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .setup(|app| {
            info!("设置应用程序...");
            
            // 初始化应用状态
            let rt = tokio::runtime::Runtime::new().unwrap();
            let app_state = rt.block_on(async {
                AppState::new().await
            });
            
            match app_state {
                Ok(state) => {
                    app.manage(Arc::new(Mutex::new(state)));
                    info!("应用状态初始化成功");
                },
                Err(e) => {
                    error!("应用状态初始化失败: {}", e);
                    return Err(Box::new(e));
                }
            }
            
            // 设置系统托盘（如果支持）
            #[cfg(desktop)]
            {
                use tauri::{
                    tray::{TrayIconBuilder, TrayIconEvent},
                    menu::{MenuBuilder, MenuItemBuilder},
                };

                let quit = MenuItemBuilder::with_id("quit", "退出").build(app)?;
                let show = MenuItemBuilder::with_id("show", "显示窗口").build(app)?;
                let menu = MenuBuilder::new(app)
                    .item(&show)
                    .separator()
                    .item(&quit)
                    .build()?;

                let _tray = TrayIconBuilder::new()
                    .menu(&menu)
                    .icon(app.default_window_icon().unwrap().clone())
                    .build(app)?;
            }
            
            info!("应用程序设置完成");
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // 资产管理命令
            commands::asset::get_assets,
            commands::asset::get_asset_by_id,
            commands::asset::create_asset,
            commands::asset::update_asset,
            commands::asset::delete_asset,
            commands::asset::search_assets,
            commands::asset::get_asset_categories,
            commands::asset::create_asset_category,
            
            // 监控命令
            commands::monitoring::get_device_status,
            commands::monitoring::get_monitoring_devices,
            commands::monitoring::add_monitoring_device,
            commands::monitoring::update_monitoring_device,
            commands::monitoring::delete_monitoring_device,
            commands::monitoring::get_device_metrics,
            commands::monitoring::start_monitoring,
            commands::monitoring::stop_monitoring,
            
            // 网络管理命令
            commands::network::scan_network,
            commands::network::get_network_devices,
            commands::network::get_bandwidth_data,
            commands::network::ping_device,
            commands::network::traceroute,
            
            // 报表命令
            commands::report::generate_asset_report,
            commands::report::generate_monitoring_report,
            commands::report::export_to_csv,
            commands::report::export_to_excel,
            
            // 系统命令
            commands::system::get_system_info,
            commands::system::get_app_config,
            commands::system::update_app_config,
            commands::system::backup_database,
            commands::system::restore_database,
            
            // 用户管理命令
            commands::user::authenticate_user,
            commands::user::get_current_user,
            commands::user::update_user_settings,
            
            // 文件操作命令
            commands::file::import_assets_from_csv,
            commands::file::export_assets_to_csv,
            commands::file::import_assets_from_excel,
            commands::file::export_assets_to_excel,

            // 维修工单命令
            commands::maintenance::create_maintenance_order,
            commands::maintenance::get_maintenance_order,
            commands::maintenance::update_maintenance_order,
            commands::maintenance::list_maintenance_orders,
            commands::maintenance::get_maintenance_stats,
            commands::maintenance::assign_maintenance_order,
            commands::maintenance::start_maintenance_order,
            commands::maintenance::complete_maintenance_order,
            commands::maintenance::cancel_maintenance_order,
            commands::maintenance::get_maintenance_order_status_history,
            commands::maintenance::get_my_maintenance_orders,
            commands::maintenance::get_overdue_maintenance_orders,

            // 数据库管理命令
            commands::database::get_database_stats,
            commands::database::backup_database,
            commands::database::restore_database,
            commands::database::optimize_database,
            commands::database::check_database_integrity,
            commands::database::cleanup_expired_data,
            commands::database::get_migration_status,
            commands::database::run_migrations,
            commands::database::rollback_migration,
            commands::database::seed_database,
            commands::database::clear_seed_data,
            commands::database::execute_sql_query,
            commands::database::get_database_schema,
            commands::database::get_table_counts,
            commands::database::test_database_connection,
            commands::database::get_database_config,
            commands::database::export_database_json,
        ])
        .on_tray_icon_event(|app, event| {
            use tauri::tray::TrayIconEvent;

            match event {
                TrayIconEvent::Click { .. } => {
                    if let Some(window) = app.get_webview_window("main") {
                        if window.is_visible().unwrap_or(false) {
                            let _ = window.hide();
                        } else {
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                },
                TrayIconEvent::MenuItemClick { id, .. } => {
                    match id.as_ref() {
                        "quit" => {
                            app.exit(0);
                        },
                        "show" => {
                            if let Some(window) = app.get_webview_window("main") {
                                let _ = window.show();
                                let _ = window.set_focus();
                            }
                        },
                        _ => {}
                    }
                },
                _ => {}
            }
        })
        .run(tauri::generate_context!())
        .expect("启动Tauri应用程序时出错");
}
