from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    InspectionTemplateViewSet,
    InspectionTaskViewSet,
    InspectionResultViewSet
)

router = DefaultRouter()
router.register(r'templates', InspectionTemplateViewSet)
router.register(r'tasks', InspectionTaskViewSet)
router.register(r'results', InspectionResultViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
