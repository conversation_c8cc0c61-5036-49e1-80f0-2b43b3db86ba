from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from typing import List, Optional
from datetime import datetime, timedelta
import json

from database import get_db
from models.monitoring import EnvironmentData, UPSData, MainsPowerData, MonitoringDevice
from schemas.monitoring import (
    EnvironmentDataCreate, EnvironmentDataResponse,
    UPSDataCreate, UPSDataResponse,
    MainsPowerDataCreate, MainsPowerDataResponse,
    MonitoringDeviceCreate, MonitoringDeviceUpdate, MonitoringDeviceResponse,
    HistoricalDataQuery
)
from .utils import generic_update_item_async # 新增导入

router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])

# 环境监控API
@router.post("/environment", response_model=EnvironmentDataResponse)
async def create_environment_data(data: EnvironmentDataCreate, db: AsyncSession = Depends(get_db)):
    """创建环境监控数据记录"""
    db_data = EnvironmentData(**data.model_dump())
    db.add(db_data)
    await db.commit()
    await db.refresh(db_data)
    return db_data


@router.get("/environment", response_model=List[EnvironmentDataResponse])
async def get_environment_data(
    device_id: Optional[str] = None,
    floor: Optional[str] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取最新环境监控数据"""
    query = select(EnvironmentData).order_by(desc(EnvironmentData.timestamp)).limit(limit)

    if device_id:
        query = query.filter(EnvironmentData.device_id == device_id)
    if floor:
        query = query.filter(EnvironmentData.floor == floor)

    result = await db.execute(query)
    return result.scalars().all()


@router.get("/environment/history", response_model=List[EnvironmentDataResponse])
async def get_environment_history(
    device_id: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取环境监控历史数据"""
    query = select(EnvironmentData).filter(EnvironmentData.device_id == device_id)

    if start_time:
        query = query.filter(EnvironmentData.timestamp >= start_time)
    if end_time:
        query = query.filter(EnvironmentData.timestamp <= end_time)

    query = query.order_by(desc(EnvironmentData.timestamp)).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


# UPS监控API
@router.post("/ups", response_model=UPSDataResponse)
async def create_ups_data(data: UPSDataCreate, db: AsyncSession = Depends(get_db)):
    """创建UPS监控数据记录"""
    db_data = UPSData(**data.model_dump())
    db.add(db_data)
    await db.commit()
    await db.refresh(db_data)
    return db_data


@router.get("/ups", response_model=List[UPSDataResponse])
async def get_ups_data(
    device_id: Optional[str] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取最新UPS监控数据"""
    query = select(UPSData).order_by(desc(UPSData.timestamp)).limit(limit)

    if device_id:
        query = query.filter(UPSData.device_id == device_id)

    result = await db.execute(query)
    return result.scalars().all()


@router.get("/ups/history", response_model=List[UPSDataResponse])
async def get_ups_history(
    device_id: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取UPS监控历史数据"""
    query = select(UPSData).filter(UPSData.device_id == device_id)

    if start_time:
        query = query.filter(UPSData.timestamp >= start_time)
    if end_time:
        query = query.filter(UPSData.timestamp <= end_time)

    query = query.order_by(desc(UPSData.timestamp)).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


# 市电监控API
@router.post("/mains", response_model=MainsPowerDataResponse)
async def create_mains_power_data(data: MainsPowerDataCreate, db: AsyncSession = Depends(get_db)):
    """创建市电监控数据记录"""
    db_data = MainsPowerData(**data.model_dump())
    db.add(db_data)
    await db.commit()
    await db.refresh(db_data)
    return db_data


@router.get("/mains", response_model=List[MainsPowerDataResponse])
async def get_mains_power_data(
    device_id: Optional[str] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取最新市电监控数据"""
    query = select(MainsPowerData).order_by(desc(MainsPowerData.timestamp)).limit(limit)

    if device_id:
        query = query.filter(MainsPowerData.device_id == device_id)

    result = await db.execute(query)
    return result.scalars().all()


@router.get("/mains/history", response_model=List[MainsPowerDataResponse])
async def get_mains_power_history(
    device_id: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """获取市电监控历史数据"""
    query = select(MainsPowerData).filter(MainsPowerData.device_id == device_id)

    if start_time:
        query = query.filter(MainsPowerData.timestamp >= start_time)
    if end_time:
        query = query.filter(MainsPowerData.timestamp <= end_time)

    query = query.order_by(desc(MainsPowerData.timestamp)).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()


# 监控设备配置API
@router.post("/devices", response_model=MonitoringDeviceResponse)
async def create_monitoring_device(device: MonitoringDeviceCreate, db: AsyncSession = Depends(get_db)):
    """创建监控设备配置"""
    db_device = MonitoringDevice(**device.model_dump())
    db.add(db_device)
    await db.commit()
    await db.refresh(db_device)
    return db_device


@router.get("/devices", response_model=List[MonitoringDeviceResponse])
async def get_monitoring_devices(
    device_type: Optional[str] = None,
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取监控设备配置列表"""
    query = select(MonitoringDevice)

    if device_type:
        query = query.filter(MonitoringDevice.device_type == device_type)
    if status:
        query = query.filter(MonitoringDevice.status == status)

    result = await db.execute(query)
    return result.scalars().all()


@router.get("/devices/{device_id}", response_model=MonitoringDeviceResponse)
async def get_monitoring_device(device_id: str, db: AsyncSession = Depends(get_db)):
    """获取监控设备配置详情"""
    result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
    device = result.scalars().first()

    if not device:
        raise HTTPException(status_code=404, detail=f"Device with ID {device_id} not found")

    return device


@router.put("/devices/{device_id}", response_model=MonitoringDeviceResponse)
async def update_monitoring_device(
    device_id: str, 
    device_update: MonitoringDeviceUpdate, 
    db: AsyncSession = Depends(get_db)
):
    """更新监控设备配置"""
    # 预更新检查：例如，检查 device_id 是否与 update_schema 中的 device_id 冲突（如果允许更新）
    # 或者其他特定于监控设备的验证逻辑
    async def _pre_update_monitoring_device_checks(db_session: AsyncSession, db_item: MonitoringDevice, update_schema: MonitoringDeviceUpdate):
        # 示例：如果设备ID在更新模式中提供并且与路径参数不匹配，则引发错误
        # (通常设备ID是不可变的，但这是一个示例)
        if update_schema.device_id and update_schema.device_id != db_item.device_id:
            # 检查新的 device_id 是否已被其他设备使用
            existing_device_with_new_id = await db_session.execute(
                select(MonitoringDevice).filter(MonitoringDevice.device_id == update_schema.device_id, MonitoringDevice.id != db_item.id)
            )
            if existing_device_with_new_id.scalars().first():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"监控设备ID '{update_schema.device_id}' 已被其他设备使用"
                )
        pass # 根据需要添加更多检查

    return await generic_update_item_async(
        db=db,
        model_cls=MonitoringDevice,
        item_id=device_id, # 假设 device_id 是主键或唯一标识符，如果不是，需要调整 id_attribute
        update_data_schema=device_update,
        item_name="监控设备",
        id_attribute="device_id", # 指定模型中用于查找的ID字段名
        pre_update_checks=_pre_update_monitoring_device_checks
    )


@router.delete("/devices/{device_id}")
async def delete_monitoring_device(device_id: str, db: AsyncSession = Depends(get_db)):
    """删除监控设备配置"""
    result = await db.execute(select(MonitoringDevice).filter(MonitoringDevice.device_id == device_id))
    device = result.scalars().first()

    if not device:
        raise HTTPException(status_code=404, detail=f"Device with ID {device_id} not found")

    await db.delete(device)
    await db.commit()

    return {"message": f"Device {device_id} deleted successfully"}
