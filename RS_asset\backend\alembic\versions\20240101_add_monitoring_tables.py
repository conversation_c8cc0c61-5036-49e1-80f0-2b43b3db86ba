"""Add monitoring tables

Revision ID: 20240101_add_monitoring_tables
Revises: 65c370f8b1c2
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240101_add_monitoring_tables'
down_revision = '65c370f8b1c2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # 创建环境监控数据表
    op.create_table(
        'environment_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('device_id', sa.String(), nullable=True),
        sa.Column('location', sa.String(), nullable=True),
        sa.Column('floor', sa.String(), nullable=True),
        sa.Column('temperature', sa.Float(), nullable=True),
        sa.Column('humidity', sa.Float(), nullable=True),
        sa.Column('smoke', sa.<PERSON>(), nullable=True),
        sa.Column('water', sa.<PERSON>(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_environment_data_device_id'), 'environment_data', ['device_id'], unique=False)
    op.create_index(op.f('ix_environment_data_timestamp'), 'environment_data', ['timestamp'], unique=False)

    # 创建UPS监控数据表
    op.create_table(
        'ups_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('device_id', sa.String(), nullable=True),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('location', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('load', sa.Float(), nullable=True),
        sa.Column('battery_level', sa.Float(), nullable=True),
        sa.Column('battery_time_remaining', sa.Integer(), nullable=True),
        sa.Column('input_voltage', sa.Float(), nullable=True),
        sa.Column('output_voltage', sa.Float(), nullable=True),
        sa.Column('input_frequency', sa.Float(), nullable=True),
        sa.Column('output_frequency', sa.Float(), nullable=True),
        sa.Column('temperature', sa.Float(), nullable=True),
        sa.Column('battery_voltage', sa.Float(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ups_data_device_id'), 'ups_data', ['device_id'], unique=False)
    op.create_index(op.f('ix_ups_data_timestamp'), 'ups_data', ['timestamp'], unique=False)

    # 创建市电监控数据表
    op.create_table(
        'mains_power_data',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('device_id', sa.String(), nullable=True),
        sa.Column('location', sa.String(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('voltage', sa.Float(), nullable=True),
        sa.Column('frequency', sa.Float(), nullable=True),
        sa.Column('current', sa.Float(), nullable=True),
        sa.Column('power', sa.Float(), nullable=True),
        sa.Column('power_factor', sa.Float(), nullable=True),
        sa.Column('energy_consumption', sa.Float(), nullable=True),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_mains_power_data_device_id'), 'mains_power_data', ['device_id'], unique=False)
    op.create_index(op.f('ix_mains_power_data_timestamp'), 'mains_power_data', ['timestamp'], unique=False)

    # 创建监控设备配置表
    op.create_table(
        'monitoring_devices',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('device_id', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=True),
        sa.Column('device_type', sa.String(), nullable=True),
        sa.Column('location', sa.String(), nullable=True),
        sa.Column('floor', sa.String(), nullable=True),
        sa.Column('protocol', sa.String(), nullable=True),
        sa.Column('host', sa.String(), nullable=True),
        sa.Column('port', sa.Integer(), nullable=True),
        sa.Column('community', sa.String(), nullable=True),
        sa.Column('version', sa.Integer(), nullable=True),
        sa.Column('oids', sa.Text(), nullable=True),
        sa.Column('status', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_monitoring_devices_device_id'), 'monitoring_devices', ['device_id'], unique=True)


def downgrade() -> None:
    # 删除监控设备配置表
    op.drop_index(op.f('ix_monitoring_devices_device_id'), table_name='monitoring_devices')
    op.drop_table('monitoring_devices')
    
    # 删除市电监控数据表
    op.drop_index(op.f('ix_mains_power_data_timestamp'), table_name='mains_power_data')
    op.drop_index(op.f('ix_mains_power_data_device_id'), table_name='mains_power_data')
    op.drop_table('mains_power_data')
    
    # 删除UPS监控数据表
    op.drop_index(op.f('ix_ups_data_timestamp'), table_name='ups_data')
    op.drop_index(op.f('ix_ups_data_device_id'), table_name='ups_data')
    op.drop_table('ups_data')
    
    # 删除环境监控数据表
    op.drop_index(op.f('ix_environment_data_timestamp'), table_name='environment_data')
    op.drop_index(op.f('ix_environment_data_device_id'), table_name='environment_data')
    op.drop_table('environment_data')
