{"name": "rs-asset", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:https": "node server.js", "build": "next build", "start": "next start", "start:https": "NODE_ENV=production node server.js", "lint": "next lint", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@next/env": "^14.2.28", "@phosphor-icons/react": "^2.1.1", "@prisma/client": "^6.6.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-dialog": "^2.3.1", "@tauri-apps/plugin-fs": "^2.4.1", "@tauri-apps/plugin-http": "^2.5.1", "@tauri-apps/plugin-log": "^2.6.0", "@tauri-apps/plugin-notification": "^2.3.0", "@tauri-apps/plugin-os": "^2.3.0", "@tauri-apps/plugin-shell": "^2.3.0", "chart.js": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "html5-qrcode": "^2.3.8", "i18next": "^23.7.16", "lucide-react": "^0.511.0", "next": "^14.2.28", "next-themes": "^0.4.6", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-i18next": "^14.0.0", "react-icons": "^5.0.1", "react-swipeable": "^7.0.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "zustand": "^5.0.4"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.7", "@tauri-apps/cli": "^2.7.1", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.21", "eslint": "^8.0.0", "eslint-config-next": "14.1.0", "postcss": "^8.5.3", "prisma": "^6.6.0", "tailwindcss": "^4.1.7", "typescript": "^5.0.0"}}