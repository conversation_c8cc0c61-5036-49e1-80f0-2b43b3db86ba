// 维修工单服务
// 提供维修工单的创建、分配、跟踪、完成等功能

use crate::error::{AppError, AppResult};
use crate::models::{MaintenanceOrder, PaginationParams, PaginationResponse};
use sqlx::{SqlitePool, Row};
use std::sync::Arc;
use uuid::Uuid;
use chrono::{DateTime, Utc, NaiveDateTime};
use serde::{Deserialize, Serialize};

/// 维修工单服务
#[derive(Debug, Clone)]
pub struct MaintenanceService {
    pool: SqlitePool,
}

/// 维修工单创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateMaintenanceOrderRequest {
    pub asset_id: i64,
    pub title: String,
    pub description: Option<String>,
    pub priority: String, // low, medium, high, urgent
    pub order_type: String, // corrective, preventive, predictive
    pub fault_type: Option<String>,
    pub fault_description: Option<String>,
    pub assigned_to: Option<i64>,
    pub assigned_team: Option<String>,
    pub estimated_hours: Option<f64>,
    pub estimated_cost: Option<f64>,
    pub scheduled_start_time: Option<String>,
    pub scheduled_end_time: Option<String>,
}

/// 维修工单更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateMaintenanceOrderRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub status: Option<String>,
    pub assigned_to: Option<i64>,
    pub assigned_team: Option<String>,
    pub estimated_hours: Option<f64>,
    pub actual_hours: Option<f64>,
    pub estimated_cost: Option<f64>,
    pub actual_cost: Option<f64>,
    pub scheduled_start_time: Option<String>,
    pub scheduled_end_time: Option<String>,
    pub actual_start_time: Option<String>,
    pub actual_end_time: Option<String>,
    pub completion_notes: Option<String>,
    pub customer_satisfaction: Option<i32>,
    pub customer_feedback: Option<String>,
}

/// 维修工单过滤器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceOrderFilter {
    pub keyword: Option<String>,
    pub asset_id: Option<i64>,
    pub status: Option<String>,
    pub priority: Option<String>,
    pub order_type: Option<String>,
    pub assigned_to: Option<i64>,
    pub date_from: Option<String>,
    pub date_to: Option<String>,
}

/// 维修工单统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MaintenanceStats {
    pub total_orders: i64,
    pub orders_by_status: std::collections::HashMap<String, i64>,
    pub orders_by_priority: std::collections::HashMap<String, i64>,
    pub orders_by_type: std::collections::HashMap<String, i64>,
    pub avg_completion_time_hours: f64,
    pub avg_customer_satisfaction: f64,
    pub overdue_orders: i64,
    pub pending_orders: i64,
}

impl MaintenanceService {
    /// 创建新的维修工单服务实例
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    /// 创建维修工单
    pub async fn create_order(&self, request: CreateMaintenanceOrderRequest, created_by: i64) -> AppResult<MaintenanceOrder> {
        // 生成工单号
        let order_number = self.generate_order_number().await?;

        let result = sqlx::query(
            r#"
            INSERT INTO maintenance_orders (
                order_number, asset_id, title, description, priority, status, order_type,
                fault_type, fault_description, reported_by, assigned_to, assigned_team,
                estimated_hours, estimated_cost, scheduled_start_time, scheduled_end_time,
                created_by, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, 'pending', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP
            )
            "#
        )
        .bind(&order_number)
        .bind(request.asset_id)
        .bind(&request.title)
        .bind(&request.description)
        .bind(&request.priority)
        .bind(&request.order_type)
        .bind(&request.fault_type)
        .bind(&request.fault_description)
        .bind(created_by)
        .bind(request.assigned_to)
        .bind(&request.assigned_team)
        .bind(request.estimated_hours)
        .bind(request.estimated_cost)
        .bind(&request.scheduled_start_time)
        .bind(&request.scheduled_end_time)
        .bind(created_by)
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("创建维修工单失败: {}", e),
        })?;

        let order_id = result.last_insert_rowid();

        // 记录状态变更
        self.log_status_change(order_id, None, "pending", "工单创建", created_by).await?;

        self.get_order_by_id(order_id).await
    }

    /// 生成工单号
    async fn generate_order_number(&self) -> AppResult<String> {
        let today = chrono::Utc::now().format("%Y%m%d").to_string();
        
        // 查询今天已有的工单数量
        let count: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM maintenance_orders WHERE order_number LIKE ?"
        )
        .bind(format!("WO-{}-%%", today))
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0);

        Ok(format!("WO-{}-{:04}", today, count + 1))
    }

    /// 记录状态变更
    async fn log_status_change(
        &self,
        order_id: i64,
        from_status: Option<&str>,
        to_status: &str,
        reason: &str,
        changed_by: i64,
    ) -> AppResult<()> {
        sqlx::query(
            r#"
            INSERT INTO maintenance_order_status_logs (
                order_id, from_status, to_status, reason, changed_by, changed_at
            ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            "#
        )
        .bind(order_id)
        .bind(from_status)
        .bind(to_status)
        .bind(reason)
        .bind(changed_by)
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("记录状态变更失败: {}", e),
        })?;

        Ok(())
    }

    /// 根据ID获取维修工单
    pub async fn get_order_by_id(&self, id: i64) -> AppResult<MaintenanceOrder> {
        let row = sqlx::query(
            r#"
            SELECT mo.*, a.name as asset_name, a.asset_code,
                   u1.full_name as reported_by_name,
                   u2.full_name as assigned_to_name,
                   u3.full_name as created_by_name,
                   u4.full_name as updated_by_name
            FROM maintenance_orders mo
            LEFT JOIN assets a ON mo.asset_id = a.id
            LEFT JOIN users u1 ON mo.reported_by = u1.id
            LEFT JOIN users u2 ON mo.assigned_to = u2.id
            LEFT JOIN users u3 ON mo.created_by = u3.id
            LEFT JOIN users u4 ON mo.updated_by = u4.id
            WHERE mo.id = ?
            "#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("查询维修工单失败: {}", e),
        })?
        .ok_or_else(|| AppError::NotFound {
            message: format!("维修工单不存在: {}", id),
        })?;

        Ok(MaintenanceOrder::from_row(&row)?)
    }

    /// 更新维修工单
    pub async fn update_order(&self, id: i64, request: UpdateMaintenanceOrderRequest, updated_by: i64) -> AppResult<MaintenanceOrder> {
        // 获取当前状态
        let current_order = self.get_order_by_id(id).await?;
        let old_status = current_order.status.clone();

        // 构建更新查询（简化版本）
        sqlx::query(
            r#"
            UPDATE maintenance_orders 
            SET updated_by = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            "#
        )
        .bind(updated_by)
        .bind(id)
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::Database {
            message: format!("更新维修工单失败: {}", e),
        })?;

        // 如果状态发生变化，记录状态变更
        if let Some(new_status) = &request.status {
            if new_status != &old_status {
                self.log_status_change(id, Some(&old_status), new_status, "状态更新", updated_by).await?;
            }
        }

        self.get_order_by_id(id).await
    }

    /// 分页查询维修工单
    pub async fn list_orders(
        &self,
        filter: Option<MaintenanceOrderFilter>,
        pagination: PaginationParams,
    ) -> AppResult<PaginationResponse<MaintenanceOrder>> {
        let mut where_clauses = Vec::new();
        let mut params: Vec<String> = Vec::new();

        // 构建查询条件
        if let Some(filter) = filter {
            if let Some(keyword) = filter.keyword {
                where_clauses.push("(mo.order_number LIKE ? OR mo.title LIKE ? OR mo.description LIKE ?)");
                let search_term = format!("%{}%", keyword);
                params.extend(vec![search_term.clone(), search_term.clone(), search_term]);
            }
            if let Some(asset_id) = filter.asset_id {
                where_clauses.push("mo.asset_id = ?");
                params.push(asset_id.to_string());
            }
            if let Some(status) = filter.status {
                where_clauses.push("mo.status = ?");
                params.push(status);
            }
            if let Some(priority) = filter.priority {
                where_clauses.push("mo.priority = ?");
                params.push(priority);
            }
        }

        let where_clause = if where_clauses.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_clauses.join(" AND "))
        };

        // 查询总数
        let count_query = format!(
            "SELECT COUNT(*) FROM maintenance_orders mo {}",
            where_clause
        );
        
        let total: i64 = sqlx::query_scalar(&count_query)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("查询维修工单总数失败: {}", e),
            })?;

        // 查询数据
        let data_query = format!(
            r#"
            SELECT mo.*, a.name as asset_name, a.asset_code,
                   u1.full_name as reported_by_name,
                   u2.full_name as assigned_to_name,
                   u3.full_name as created_by_name,
                   u4.full_name as updated_by_name
            FROM maintenance_orders mo
            LEFT JOIN assets a ON mo.asset_id = a.id
            LEFT JOIN users u1 ON mo.reported_by = u1.id
            LEFT JOIN users u2 ON mo.assigned_to = u2.id
            LEFT JOIN users u3 ON mo.created_by = u3.id
            LEFT JOIN users u4 ON mo.updated_by = u4.id
            {}
            ORDER BY mo.created_at DESC
            LIMIT ? OFFSET ?
            "#,
            where_clause
        );

        let offset = (pagination.page - 1) * pagination.page_size;
        let rows = sqlx::query(&data_query)
            .bind(pagination.page_size as i64)
            .bind(offset as i64)
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::Database {
                message: format!("查询维修工单列表失败: {}", e),
            })?;

        let mut orders = Vec::new();
        for row in rows {
            orders.push(MaintenanceOrder::from_row(&row)?);
        }

        Ok(PaginationResponse {
            data: orders,
            total,
            page: pagination.page,
            page_size: pagination.page_size,
            total_pages: (total as f64 / pagination.page_size as f64).ceil() as i64,
        })
    }

    /// 获取维修工单统计信息
    pub async fn get_maintenance_stats(&self) -> AppResult<MaintenanceStats> {
        // 总工单数
        let total_orders: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM maintenance_orders")
            .fetch_one(&self.pool)
            .await
            .unwrap_or(0);

        // 按状态统计
        let status_rows = sqlx::query("SELECT status, COUNT(*) as count FROM maintenance_orders GROUP BY status")
            .fetch_all(&self.pool)
            .await
            .unwrap_or_default();
        
        let mut orders_by_status = std::collections::HashMap::new();
        for row in status_rows {
            let status: String = row.get("status");
            let count: i64 = row.get("count");
            orders_by_status.insert(status, count);
        }

        // 按优先级统计
        let priority_rows = sqlx::query("SELECT priority, COUNT(*) as count FROM maintenance_orders GROUP BY priority")
            .fetch_all(&self.pool)
            .await
            .unwrap_or_default();
        
        let mut orders_by_priority = std::collections::HashMap::new();
        for row in priority_rows {
            let priority: String = row.get("priority");
            let count: i64 = row.get("count");
            orders_by_priority.insert(priority, count);
        }

        // 按类型统计
        let type_rows = sqlx::query("SELECT order_type, COUNT(*) as count FROM maintenance_orders GROUP BY order_type")
            .fetch_all(&self.pool)
            .await
            .unwrap_or_default();
        
        let mut orders_by_type = std::collections::HashMap::new();
        for row in type_rows {
            let order_type: String = row.get("order_type");
            let count: i64 = row.get("count");
            orders_by_type.insert(order_type, count);
        }

        // 平均完成时间
        let avg_completion_time_hours: f64 = sqlx::query_scalar(
            "SELECT AVG(actual_hours) FROM maintenance_orders WHERE actual_hours IS NOT NULL"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0.0);

        // 平均客户满意度
        let avg_customer_satisfaction: f64 = sqlx::query_scalar(
            "SELECT AVG(customer_satisfaction) FROM maintenance_orders WHERE customer_satisfaction IS NOT NULL"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0.0);

        // 逾期工单数
        let overdue_orders: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM maintenance_orders WHERE scheduled_end_time IS NOT NULL AND DATE(scheduled_end_time) < DATE('now') AND status NOT IN ('completed', 'cancelled')"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0);

        // 待处理工单数
        let pending_orders: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM maintenance_orders WHERE status = 'pending'"
        )
        .fetch_one(&self.pool)
        .await
        .unwrap_or(0);

        Ok(MaintenanceStats {
            total_orders,
            orders_by_status,
            orders_by_priority,
            orders_by_type,
            avg_completion_time_hours,
            avg_customer_satisfaction,
            overdue_orders,
            pending_orders,
        })
    }
}
