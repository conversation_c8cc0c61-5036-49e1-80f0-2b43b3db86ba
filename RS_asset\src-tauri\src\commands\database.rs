// 数据库管理相关的 Tauri 命令

use crate::database::{DatabaseManager, DatabaseStats, MigrationManager, SeedManager};
use crate::error::{AppError, AppResult};
use crate::AppState;
use std::path::PathBuf;
use tauri::State;

/// 获取数据库统计信息
#[tauri::command]
pub async fn get_database_stats(state: State<'_, AppState>) -> Result<DatabaseStats, String> {
    let db = &state.database;
    db.get_stats().await.map_err(|e| e.to_string())
}

/// 备份数据库
#[tauri::command]
pub async fn backup_database(
    backup_path: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let db = &state.database;
    let path = PathBuf::from(backup_path);
    
    db.backup(&path).await.map_err(|e| e.to_string())?;
    
    Ok(format!("数据库已成功备份到: {}", path.display()))
}

/// 恢复数据库
#[tauri::command]
pub async fn restore_database(
    backup_path: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let db = &state.database;
    let path = PathBuf::from(backup_path);
    
    db.restore(&path).await.map_err(|e| e.to_string())?;
    
    Ok(format!("数据库已从备份恢复: {}", path.display()))
}

/// 优化数据库
#[tauri::command]
pub async fn optimize_database(state: State<'_, AppState>) -> Result<String, String> {
    let db = &state.database;
    db.optimize().await.map_err(|e| e.to_string())?;
    Ok("数据库优化完成".to_string())
}

/// 检查数据库完整性
#[tauri::command]
pub async fn check_database_integrity(state: State<'_, AppState>) -> Result<bool, String> {
    let db = &state.database;
    db.check_integrity().await.map_err(|e| e.to_string())
}

/// 清理过期数据
#[tauri::command]
pub async fn cleanup_expired_data(state: State<'_, AppState>) -> Result<String, String> {
    let db = &state.database;
    db.cleanup_expired_data().await.map_err(|e| e.to_string())?;
    Ok("过期数据清理完成".to_string())
}

/// 获取迁移状态
#[tauri::command]
pub async fn get_migration_status(
    state: State<'_, AppState>,
) -> Result<std::collections::HashMap<String, bool>, String> {
    let migration_manager = MigrationManager::new(state.database.pool().clone());
    migration_manager.get_migration_status().await.map_err(|e| e.to_string())
}

/// 运行数据库迁移
#[tauri::command]
pub async fn run_migrations(state: State<'_, AppState>) -> Result<String, String> {
    let migration_manager = MigrationManager::new(state.database.pool().clone());
    migration_manager.run_migrations().await.map_err(|e| e.to_string())?;
    Ok("数据库迁移完成".to_string())
}

/// 回滚迁移
#[tauri::command]
pub async fn rollback_migration(
    version: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    let migration_manager = MigrationManager::new(state.database.pool().clone());
    migration_manager.rollback_migration(&version).await.map_err(|e| e.to_string())?;
    Ok(format!("迁移 {} 已回滚", version))
}

/// 生成种子数据
#[tauri::command]
pub async fn seed_database(state: State<'_, AppState>) -> Result<String, String> {
    let seed_manager = SeedManager::new(state.database.pool().clone());
    seed_manager.seed_all().await.map_err(|e| e.to_string())?;
    Ok("种子数据生成完成".to_string())
}

/// 清除种子数据
#[tauri::command]
pub async fn clear_seed_data(state: State<'_, AppState>) -> Result<String, String> {
    let seed_manager = SeedManager::new(state.database.pool().clone());
    seed_manager.clear_seed_data().await.map_err(|e| e.to_string())?;
    Ok("种子数据清除完成".to_string())
}

/// 执行自定义SQL查询（仅管理员）
#[tauri::command]
pub async fn execute_sql_query(
    query: String,
    state: State<'_, AppState>,
) -> Result<serde_json::Value, String> {
    // 注意：这是一个危险的操作，应该只允许管理员使用
    // 在实际应用中，应该添加权限检查
    
    if query.trim().to_uppercase().starts_with("SELECT") {
        // 只允许SELECT查询
        let rows = sqlx::query(&query)
            .fetch_all(state.database.pool())
            .await
            .map_err(|e| format!("查询执行失败: {}", e))?;
        
        let mut results = Vec::new();
        for row in rows {
            let mut record = serde_json::Map::new();
            for (i, column) in row.columns().iter().enumerate() {
                let value: Option<String> = row.try_get(i).ok();
                record.insert(
                    column.name().to_string(),
                    serde_json::Value::String(value.unwrap_or_default()),
                );
            }
            results.push(serde_json::Value::Object(record));
        }
        
        Ok(serde_json::Value::Array(results))
    } else {
        Err("只允许执行SELECT查询".to_string())
    }
}

/// 获取数据库表信息
#[tauri::command]
pub async fn get_database_schema(state: State<'_, AppState>) -> Result<Vec<TableInfo>, String> {
    let query = r#"
        SELECT 
            name as table_name,
            sql as create_sql
        FROM sqlite_master 
        WHERE type = 'table' 
        AND name NOT LIKE 'sqlite_%'
        ORDER BY name
    "#;
    
    let rows = sqlx::query_as::<_, TableInfo>(query)
        .fetch_all(state.database.pool())
        .await
        .map_err(|e| format!("获取数据库结构失败: {}", e))?;
    
    Ok(rows)
}

/// 获取表的记录数
#[tauri::command]
pub async fn get_table_counts(state: State<'_, AppState>) -> Result<std::collections::HashMap<String, i64>, String> {
    let tables = vec![
        "assets", "asset_categories", "users", "roles", "user_roles",
        "maintenance_orders", "spare_parts", "inspection_plans", 
        "inspection_records", "notifications", "documents", "audit_logs"
    ];
    
    let mut counts = std::collections::HashMap::new();
    
    for table in tables {
        let query = format!("SELECT COUNT(*) as count FROM {}", table);
        let count: i64 = sqlx::query_scalar(&query)
            .fetch_one(state.database.pool())
            .await
            .unwrap_or(0);
        counts.insert(table.to_string(), count);
    }
    
    Ok(counts)
}

/// 数据库表信息结构
#[derive(Debug, serde::Serialize, sqlx::FromRow)]
pub struct TableInfo {
    pub table_name: String,
    pub create_sql: Option<String>,
}

/// 数据库连接测试
#[tauri::command]
pub async fn test_database_connection(state: State<'_, AppState>) -> Result<String, String> {
    // 执行一个简单的查询来测试连接
    let result: i64 = sqlx::query_scalar("SELECT 1")
        .fetch_one(state.database.pool())
        .await
        .map_err(|e| format!("数据库连接测试失败: {}", e))?;
    
    if result == 1 {
        Ok("数据库连接正常".to_string())
    } else {
        Err("数据库连接异常".to_string())
    }
}

/// 获取数据库配置信息
#[tauri::command]
pub async fn get_database_config(state: State<'_, AppState>) -> Result<DatabaseConfig, String> {
    let config = DatabaseConfig {
        db_path: state.database.db_path().to_string(),
        pool_size: 10, // 从配置中获取
        version: "2.0.0".to_string(),
        journal_mode: "WAL".to_string(),
        synchronous: "NORMAL".to_string(),
    };
    
    Ok(config)
}

/// 数据库配置信息结构
#[derive(Debug, serde::Serialize)]
pub struct DatabaseConfig {
    pub db_path: String,
    pub pool_size: u32,
    pub version: String,
    pub journal_mode: String,
    pub synchronous: String,
}

/// 导出数据库数据为JSON
#[tauri::command]
pub async fn export_database_json(
    tables: Vec<String>,
    state: State<'_, AppState>,
) -> Result<serde_json::Value, String> {
    let mut export_data = serde_json::Map::new();
    
    for table in tables {
        let query = format!("SELECT * FROM {}", table);
        let rows = sqlx::query(&query)
            .fetch_all(state.database.pool())
            .await
            .map_err(|e| format!("导出表 {} 失败: {}", table, e))?;
        
        let mut table_data = Vec::new();
        for row in rows {
            let mut record = serde_json::Map::new();
            for (i, column) in row.columns().iter().enumerate() {
                let value: Option<String> = row.try_get(i).ok();
                record.insert(
                    column.name().to_string(),
                    serde_json::Value::String(value.unwrap_or_default()),
                );
            }
            table_data.push(serde_json::Value::Object(record));
        }
        
        export_data.insert(table, serde_json::Value::Array(table_data));
    }
    
    Ok(serde_json::Value::Object(export_data))
}
