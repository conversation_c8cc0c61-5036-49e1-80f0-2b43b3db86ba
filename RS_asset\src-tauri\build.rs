use std::env;
use std::process::Command;

fn main() {
    // 设置构建时间
    println!("cargo:rustc-env=BUILD_DATE={}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));

    // 设置 Git 提交哈希
    if let Ok(output) = Command::new("git").args(&["rev-parse", "HEAD"]).output() {
        let git_hash = String::from_utf8(output.stdout).unwrap_or_default();
        println!("cargo:rustc-env=GIT_COMMIT={}", git_hash.trim());
    }

    // 设置 Rust 版本
    if let Ok(output) = Command::new("rustc").args(&["--version"]).output() {
        let rust_version = String::from_utf8(output.stdout).unwrap_or_default();
        println!("cargo:rustc-env=RUST_VERSION={}", rust_version.trim());
    }

    // 运行 Tauri 构建
    tauri_build::build()
}
