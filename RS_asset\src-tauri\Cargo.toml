[package]
name = "rs-asset"
version = "2.0.0"
description = "燃石资产管理系统 - 基于Tauri 2.0的桌面应用"
authors = ["Burning Rock <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/burningrock/rs-asset"
edition = "2021"
rust-version = "1.75"
keywords = ["asset-management", "monitoring", "tauri", "desktop"]
categories = ["gui", "network-programming"]

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.7.0", features = ["codegen"] }
chrono = "0.4"

[dependencies]
# Tauri 核心依赖
tauri = { version = "2.7.0", features = [
    "macos-private-api",
    "protocol-asset",
    "icon-ico",
    "icon-png",
    "tray-icon"
] }

# Tauri 插件
tauri-plugin-log = "2.6.0"
tauri-plugin-fs = "2.4.1"
tauri-plugin-dialog = "2.3.1"
tauri-plugin-http = "2.5.1"
tauri-plugin-shell = "2.3.0"
tauri-plugin-notification = "2.3.0"
tauri-plugin-os = "2.3.0"
tauri-plugin-global-shortcut = "2.3.0"

# 序列化和JSON处理
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 异步运行时
tokio = { version = "1.35", features = ["full"] }
futures = "0.3"

# 数据库相关
sqlx = { version = "0.7", features = [
    "runtime-tokio-rustls",
    "sqlite",
    "chrono",
    "uuid",
    "migrate",
    "macros"
] }

# HTTP客户端
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID生成
uuid = { version = "1.6", features = ["v4", "serde"] }

# 日志系统
log = "0.4"
env_logger = "0.10"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 密码哈希
bcrypt = "0.15"

# 网络和SNMP
snmp = "0.9"
ping = "0.5"
network-interface = "1.1"

# 配置管理
config = "0.14"
toml = "0.8"

# 加密和安全
sha2 = "0.10"
bcrypt = "0.15"
rand = "0.8"

# 文件处理
csv = "1.3"
xlsx_writer = "0.6"

# 系统信息
sysinfo = "0.30"

# 正则表达式
regex = "1.10"

# 并发处理
rayon = "1.8"
dashmap = "5.5"

# 开发依赖
[dev-dependencies]
tokio-test = "0.4"
mockall = "0.12"
tempfile = "3.8"
